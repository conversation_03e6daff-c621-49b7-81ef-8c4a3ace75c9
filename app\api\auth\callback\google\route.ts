import { NextRequest, NextResponse } from 'next/server';
import { getGoogleRedirectUri } from '@/lib/googleAuth';

export async function GET(request: NextRequest) {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    console.log('🔍 Google OAuth callback received:', {
        code: !!code,
        state,
        error,
        url: request.url
    });

    // Handle OAuth error
    if (error) {
        console.error('❌ Google OAuth error:', error);
        return NextResponse.redirect(
            new URL(`/login?error=${encodeURIComponent(error)}`, request.url)
        );
    }

    // Handle missing code
    if (!code) {
        console.error('❌ No authorization code received');
        return NextResponse.redirect(
            new URL('/login?error=no_code', request.url)
        );
    }

    try {
        // Exchange code for access token
        console.log('🔄 Exchanging code for access token...');
        const redirectUri = getGoogleRedirectUri();
        console.log('🔗 Using redirect URI for token exchange:', redirectUri);

        const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
                client_secret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET || '',
                code,
                grant_type: 'authorization_code',
                redirect_uri: redirectUri,
            }),
        });

        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            console.error('❌ Token exchange failed:', errorText);
            throw new Error('Failed to exchange code for token');
        }

        const tokenData = await tokenResponse.json();
        console.log('✅ Token exchange successful');

        // Get user info from Google
        console.log('🔄 Fetching user info from Google...');
        const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
            headers: {
                Authorization: `Bearer ${tokenData.access_token}`,
            },
        });

        if (!userResponse.ok) {
            throw new Error('Failed to fetch user info');
        }

        const userData = await userResponse.json();
        console.log('✅ User info fetched:', {
            email: userData.email,
            name: userData.name
        });

        // Gọi API backend để đăng nhập
        console.log('🔄 Calling backend API for Google login...');

        const backendResponse = await fetch(`${process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL}/api/v1/user/login/google`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: userData.email,
                name: userData.name,
                avatar: userData.picture || '',
                google_id: userData.id,
                website_id: state || process.env.NEXT_PUBLIC_WEBSITE_ID,
                ref: 'thuytx'
            }),
        });

        if (!backendResponse.ok) {
            throw new Error('Backend API call failed');
        }

        const backendData = await backendResponse.json();
        console.log('✅ Backend response:', backendData);

        // Kiểm tra response format: { error: false, status: 200, msg: "...", data: "token" }
        if (backendData.error === false && backendData.status === 200 && backendData.data) {
            // Tạo success page với token
            const successPageUrl = new URL('/auth/google/success', request.url);
            successPageUrl.searchParams.set('token', backendData.data);
            successPageUrl.searchParams.set('message', backendData.msg || 'Đăng nhập thành công!');
            successPageUrl.searchParams.set('user_data', JSON.stringify({
                email: userData.email,
                name: userData.name,
                avatar: userData.picture || '',
                google_id: userData.id,
            }));

            return NextResponse.redirect(successPageUrl);
        } else {
            throw new Error(backendData.msg || 'Đăng nhập thất bại');
        }

    } catch (error: any) {
        console.error('❌ Google OAuth callback error:', error);
        return NextResponse.redirect(
            new URL(`/login?error=${encodeURIComponent(error.message)}`, request.url)
        );
    }
}
