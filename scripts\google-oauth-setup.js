#!/usr/bin/env node

/**
 * Script để hiển thị thông tin cấu hình Google OAuth
 */

console.log("🔧 GOOGLE OAUTH CONFIGURATION GUIDE");
console.log("=====================================\n");

console.log("📋 THÔNG TIN CẦN CẤU HÌNH TRONG GOOGLE CLOUD CONSOLE:");
console.log("URL: https://console.cloud.google.com/\n");

console.log(
  "1️⃣ ORIGINES JAVASCRIPT AUTORISÉES (Authorized JavaScript origins):"
);
console.log("   - http://localhost:3000");
console.log("   - http://localhost:3001");
console.log("   - https://agent.trinhxuanthuy.id.vn");
console.log("");

console.log("2️⃣ URI DE REDIRECTION AUTORISÉS (Authorized redirect URIs):");
console.log("   ✅ http://localhost:3000/api/auth/callback/google (HIỆN TẠI)");
console.log("   ⚠️  http://localhost:3001/api/auth/callback/google (THÊM VÀO)");
console.log(
  "   ⚠️  https://agent.trinhxuanthuy.id.vn/api/auth/callback/google (THÊM VÀO)"
);
console.log("");

console.log("📝 HƯỚNG DẪN CHI TIẾT:");
console.log("1. Truy cập Google Cloud Console");
console.log("2. Chọn project hoặc tạo project mới");
console.log('3. Vào "APIs & Services" → "Credentials"');
console.log("4. Click vào OAuth 2.0 Client ID của bạn");
console.log("5. Thêm tất cả các URI ở trên vào phần tương ứng");
console.log("6. Lưu thay đổi");
console.log("");

console.log("🔑 CLIENT ID HIỆN TẠI:");
console.log(
  "   1037683057246-g38hdvnob34u3ghth264t5lbhd4ioo08.apps.googleusercontent.com"
);
console.log("");

console.log("⚠️  LƯU Ý QUAN TRỌNG:");
console.log("   - Ứng dụng hiện đang chạy trên port 3001");
console.log(
  "   - Đảm bảo tất cả các URI redirect đều được thêm vào Google Console"
);
console.log("   - Sau khi cập nhật, có thể mất vài phút để có hiệu lực");
console.log("   - Sử dụng NextAuth.js để xử lý OAuth flow");
console.log("");

console.log("🚀 KIỂM TRA:");
console.log("   - Mở http://localhost:3001/login");
console.log('   - Click nút "Debug OAuth" để xem thông tin chi tiết');
console.log("   - Thử đăng nhập với Google");
console.log("");

console.log("✅ Hoàn thành cấu hình và test lại!");
