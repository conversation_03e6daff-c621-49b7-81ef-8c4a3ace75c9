'use client'

import React from 'react'
import { useVerifiedWebsiteInfo } from '@/hooks/useDomainVerification'
import { usePathname } from 'next/navigation'

interface AppContentProps {
    children: React.ReactNode
}

export const AppContent = ({ children }: AppContentProps) => {
    const { isVerified, websiteInfo } = useVerifiedWebsiteInfo()
    const pathname = usePathname()

    // Các trang không cần domain verification
    const publicPages = ['/welcome', '/error', '/login', '/logout']
    const isPublicPage = publicPages.includes(pathname)

    // Nếu là trang public hoặc đã verify thành công, render children
    if (isPublicPage || isVerified) {
        // Log thông tin website đã được verify (chỉ khi verify thành công)
        if (isVerified && websiteInfo) {
            console.log('✅ Website info loaded successfully:', {
                title: websiteInfo.meta_title,
                description: websiteInfo.meta_description,
                logo: websiteInfo.logo,
                favicon: websiteInfo.favicon
            })
        }

        return <>{children}</>
    }

    // Fallback - không nên xảy ra vì DomainAuthProvider đã xử lý
    return null
}
