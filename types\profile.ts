// User profile interfaces
export interface UserProfileResponse {
    error: boolean;
    status: number;
    msg: string;
    data: {
        user: {
            _id: string;
            name: string;
            email: string;
            username: string;
            email_verify: number;
            google_id: string;
            website_id: string;
            ref: string | null;
            status: number;
        };
        shop: {
            status: number;
            created_at: string;
            updated_at: string;
            _id: string;
            website_id: string;
            name: string;
            email: string;
            username: string;
            ref: string | null;
            api_token: string;
        };
        package: {
            package_id: number;
            month: number;
            agent: number;
            agent_used: number;
            capacity: number;
            credit: number;
            credit_use: number;
            credits_bonus: number;
            credits_bonus_used: number;
            package_system_id: number;
            expired_at: string | null;
            created_at: string;
            updated_at: string;
            _id: string;
            shop_id: string;
        };
    };
}

