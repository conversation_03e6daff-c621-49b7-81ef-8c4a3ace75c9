import { create } from 'zustand';

export interface WebsiteInfo {
    meta_title: string;
    meta_description: string;
    favicon: string;
    logo: string;
    thumbnail: string;
}

export interface WebsiteAuth {
    google_login: number;
    client_id: string;
    client_secret: string;
    website_id: string;
}

interface DomainData {
    prompt: {
        _id: string;
        website: WebsiteInfo;
        shop_id: string;
    };
    website: {
        website_id: string;
        google_login: number;
        client_id: string;
        client_secret: string;
    };
}

interface WebsiteStore {
    // Domain verification status
    isVerified: boolean;
    isLoading: boolean;
    error: string | null;
    currentDomain: string | null;

    // Basic domain data
    promptId: string | null;
    websiteId: string | null;
    shopId: string | null;

    // Detailed website info
    websiteInfo: WebsiteInfo | null;
    websiteAuth: WebsiteAuth | null;

    // Actions
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    setDomainData: (data: DomainData, domain: string) => void;
    setWebsiteInfo: (info: WebsiteInfo) => void;
    setWebsiteAuth: (auth: WebsiteAuth) => void;
    loadFromCache: (domain: string) => boolean;
    reset: () => void;
}

export const useWebsiteStore = create<WebsiteStore>((set) => ({
    // Initial state
    isVerified: false,
    isLoading: false,
    error: null,
    currentDomain: null,
    promptId: null,
    websiteId: null,
    shopId: null,
    websiteInfo: null,
    websiteAuth: null,

    // Actions
    setLoading: (loading) => {
        console.log('🔄 Setting loading:', loading);
        set({ isLoading: loading });
    },

    setError: (error) => {
        console.log('❌ Setting error:', error);
        set({ error });
    },

    setDomainData: (data, domain) => {
        console.log('🔄 Setting domain data for domain:', domain);
        console.log('📥 Raw data received:', data);

        // Validate data structure
        if (!data || typeof data !== 'object') {
            console.error('❌ Invalid data structure - data is not an object:', data);
            set({ error: 'Invalid response data structure', isLoading: false });
            return;
        }

        if (!data.prompt || typeof data.prompt !== 'object') {
            console.error('❌ Invalid data structure - missing prompt:', data);
            set({ error: 'Missing prompt data in response', isLoading: false });
            return;
        }

        if (!data.prompt.website || typeof data.prompt.website !== 'object') {
            console.error('❌ Invalid data structure - missing prompt.website:', data.prompt);
            set({ error: 'Missing website info in prompt data', isLoading: false });
            return;
        }

        if (!data.website || typeof data.website !== 'object') {
            console.error('❌ Invalid data structure - missing website:', data);
            set({ error: 'Missing website auth data in response', isLoading: false });
            return;
        }

        try {
            const websiteInfo: WebsiteInfo = {
                meta_title: data.prompt.website.meta_title || '',
                meta_description: data.prompt.website.meta_description || '',
                favicon: data.prompt.website.favicon || '',
                logo: data.prompt.website.logo || '',
                thumbnail: data.prompt.website.thumbnail || ''
            };

            const websiteAuth: WebsiteAuth = {
                google_login: data.website.google_login || 0,
                client_id: data.website.client_id || '',
                client_secret: data.website.client_secret || '',
                website_id: data.website.website_id || ''
            };

            set({
                currentDomain: domain,
                promptId: data.prompt._id || null,
                websiteId: data.website.website_id || null,
                shopId: data.prompt.shop_id || null,
                websiteInfo,
                websiteAuth,
                isVerified: true,
                isLoading: false,
                error: null
            });

            // Không lưu domain verification status vào cookie nữa
            console.log('✅ Domain verification completed (no persistence)');

            console.log('✅ Domain data saved to store');
            console.log('📊 WebsiteInfo:', websiteInfo);
            console.log('🔐 WebsiteAuth:', websiteAuth);
        } catch (error) {
            console.error('❌ Error processing domain data:', error);
            set({
                error: `Error processing domain data: ${error instanceof Error ? error.message : 'Unknown error'}`,
                isLoading: false
            });
        }
    },

    setWebsiteInfo: (info) => {
        console.log('🔄 Setting website info:', info);
        set({ websiteInfo: info });
        console.log('✅ Website info saved to store');
    },

    setWebsiteAuth: (auth) => {
        console.log('🔄 Setting website auth:', auth);
        set({ websiteAuth: auth });
        console.log('✅ Website auth saved to store');
    },

    loadFromCache: (domain) => {
        console.log('🔄 Domain verification cache disabled for:', domain);

        // Không sử dụng cache nữa - luôn xác thực lại
        console.log('❌ Cache disabled - will verify domain again');
        return false;
    },

    reset: () => {
        console.log('🔄 Resetting website store');

        // Không cần xóa cookie vì không lưu domain verification nữa
        set({
            isVerified: false,
            isLoading: false,
            error: null,
            currentDomain: null,
            promptId: null,
            websiteId: null,
            shopId: null,
            websiteInfo: null,
            websiteAuth: null
        });
    }
}));
