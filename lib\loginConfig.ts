interface WebsiteInfo {
  meta_title?: string
  meta_description?: string
  favicon?: string
  logo?: string
  thumbnail?: string
}

interface LoginConfig {
  canLogin: boolean
  message?: string
  showGoogleLogin: boolean
  showEmailLogin: boolean
}

export const getLoginConfig = (websiteInfo: WebsiteInfo | null, isLoading: boolean = false): LoginConfig => {
  // Luôn cho phép hiển thị form đăng nhập
  return {
    canLogin: true,
    showGoogleLogin: true, // Enable Google login
    showEmailLogin: true,  // Enable Email login
    message: isLoading ? 'Đang tải thông tin website...' : undefined
  }
}
