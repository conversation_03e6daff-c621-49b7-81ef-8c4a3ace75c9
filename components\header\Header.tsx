"use client";
import { ThemeToggle } from '@/components/ui/ThemeToggle'; import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import UserProfileInfo from '../profile/UserProfileInfo';
import { SidebarTrigger } from '../ui/sidebar';
export default function Header() {
    const { user, isAuthenticated, token } = useAuth();
    const [profile, setProfile] = useState<any>(null);

    useEffect(() => {
        if (isAuthenticated && user) {
            setProfile(user);
        }
    }, [isAuthenticated, user]);

    return (
        <header className="sticky top-0 z-20 w-full border-b border-gray-200 bg-white dark:border-slate-700 dark:bg-gray-900 flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
                <SidebarTrigger className="-ml-1" />
                <span className="flex size-9 rounded-full overflow-hidden">
                    <img src="/favicon.ico" alt="Logo Chat AI" className="object-cover" />
                </span>
                <h1 className="font-bold text-gray-800 dark:text-slate-100 text-lg">Chat AI</h1>
            </div>

            <div className="flex items-center justify-end gap-3 min-w-[200px]">
                <ThemeToggle />
                <UserProfileInfo
                    user={
                        profile
                            ? {
                                name: profile.user?.name || profile.name || "",
                                email: profile.user?.email || profile.email || "",
                                avatar: profile.user?.avatar || profile.avatar || "",
                                credit: profile.package?.credit || profile.credit || 0,
                                creditUse: profile.package?.credit_use || profile.creditUse || 0,
                            }
                            : {
                                name: "",
                                email: "",
                                avatar: "",
                                credit: 0,
                                creditUse: 0,
                            }
                    }
                />

            </div>
        </header>
    )
}  
