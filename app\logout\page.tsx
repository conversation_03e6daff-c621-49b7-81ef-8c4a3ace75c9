'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { clearAuthData } from '@/lib/auth'

export default function LogoutPage() {
    const router = useRouter()

    useEffect(() => {
        // Clear auth data
        clearAuthData()

        // Redirect to home after a short delay
        const timer = setTimeout(() => {
            router.push('/')
        }, 2000)

        return () => clearTimeout(timer)
    }, [router])

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-lg text-center">
                <div className="text-blue-500 text-6xl mb-4">👋</div>
                <h2 className="text-xl font-semibold text-gray-800 mb-2">Đăng xuất thành công</h2>
                <p className="text-gray-600 mb-4">Cảm ơn bạn đ<PERSON> sử dụng dịch vụ</p>
                <p className="text-sm text-gray-500"><PERSON><PERSON> chuyển hướng về trang chủ...</p>

                <div className="mt-6">
                    <button
                        type="button"
                        onClick={() => router.push('/')}
                        className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 transition-colors"
                    >
                        Về trang chủ ngay
                    </button>
                </div>
            </div>
        </div>
    )
}