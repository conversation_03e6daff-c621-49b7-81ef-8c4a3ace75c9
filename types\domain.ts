export interface DomainResponse<T = string> {
    error: boolean;
    status: number;
    msg: string;
    data: string; // chuỗi mã hóa, bạn c<PERSON> thể decode riêng và dùng type dưới
}


//data sau giải mẫ
export interface DecodedDomainData {
    prompt: PromptInfo;
    website: WebsiteInfo;
}

//data prpompt
export interface PromptInfo {
    setting: PromptSetting;
    website: PromptWebsite;
    is_public: number;
    type: number;
    credits: number;
    credits_bonus: number;
    users: any; // Nếu bạn biết rõ kiểu, nên thay `any`
    conversations: number;
    favourites: number;
    shares: any;
    created_at: string;
    updated_at: string;
    _id: string;
    id: string;
    id_hash: string;
    name: string;
    package_id: string;
    likes: string;
    uses: string;
    lang_code: string;
    description: string;
    icon: string;
    help_url: string;
    result: any;
    status: string;
    options: any;
    option_status: string;
    avatar_url: string;
    bookmark: string;
    question: string | null;
    thread_id: string;
    one_step: string;
    model_type: string;
    chat_initiation_tool: string;
    instruction: string;
    file: string;
    assistant_id: string;
    auto_suggestion: string;
    shortcut_button: any;
    opening_text: string;
    vector_store_ids: string;
    shop_id: string;
    shop_id_owner: string;
}


export interface PromptSetting {
    suggest: string[];
    main_color: string;
    text_color: string;
    first_mess_time: string;
    first_mess_retime: string;
    option_chat_corner: string;
    icon_width: number;
    margin_x: string;
    margin_y: string;
    margin_x_mobile: string;
    margin_y_mobile: string;
    icon: string;
    icon_height: number;
    display_greeting: string;
    allows_drag: string;
    btn_start: string;
    greeting: string;
    place_input: string;
    slogan: string;
}

export interface PromptWebsite {
    status: number;
    domain: string;
    meta_title: string;
    meta_description: string;
    favicon: string;
    logo: string;
    thumbnail: string;
}


//data website
export interface WebsiteInfo {
    type: string;
    google_login: number;
    client_id: string;
    client_secret: string;
    website_id: string;
}
