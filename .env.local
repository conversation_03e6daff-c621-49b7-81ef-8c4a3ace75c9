# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production

# Google OAuth Redirect URI (for reference)
# Add these to Google Cloud Console:
# - http://localhost:3000/api/auth/callback/google
# - http://localhost:3001/api/auth/callback/google
# - http://localhost:3000/login
# - http://localhost:3001/login
# - http://localhost:3000/register
# - http://localhost:3001/register
# - https://agent.trinhxuanthuy.id.vn/api/auth/callback/google

# Google OAuth Configuration
# Get these from Google Cloud Console: https://console.cloud.google.com/
GOOGLE_CLIENT_ID=1037683057246-g38hdvnob34u3ghth264t5lbhd4ioo08.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Public Google OAuth (for frontend)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=1037683057246-g38hdvnob34u3ghth264t5lbhd4ioo08.apps.googleusercontent.com
NEXT_PUBLIC_GOOGLE_CLIENT_SECRET=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

# Site Configuration
NEXT_PUBLIC_URL=http://localhost:3000

# Test domain for localhost development
NEXT_PUBLIC_TEST_DOMAIN=agent.trinhxuanthuy.id.vn

# Google Site Verification (optional)
GOOGLE_SITE_VERIFICATION=your-google-site-verification-code

CRYPTOJS_SECRET_KEY="ultramailerCryptoTokenTrackEmail"
NEXT_PUBLIC_CRYPTOJS_SECRET_KEY="ultramailerCryptoTokenTrackEmail"

# API Configuration
NEXT_PUBLIC_NODE_API_BACKEND_URL="https://fchatai-api.salekit.com:3034"
NEXT_PUBLIC_WEBSITE_ID="684296d93545562647c5f213"
NEXT_PUBLIC_REF="thuytx"
NEXT_PUBLIC_PACKAGE_ID="1"