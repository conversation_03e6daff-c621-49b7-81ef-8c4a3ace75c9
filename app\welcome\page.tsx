"use client";
import { useRouter } from "next/navigation";

export default function WelcomePage() {
    const router = useRouter();
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-white dark:bg-gray-900">
            <div className="flex flex-col items-center gap-6 p-8 rounded-2xl shadow-lg bg-white/80 dark:bg-gray-900/80 border border-gray-200 dark:border-slate-700">
                <img src="/favicon.ico" alt="Logo Đom Đóm AI" width={80} height={80} className="rounded-full" />
                <h1 className="text-3xl font-bold text-gray-900 dark:text-slate-100 text-center">Chào mừng đến với Đom Đóm AI</h1>
                <p className="text-gray-600 dark:text-slate-400 text-center max-w-md">Tr<PERSON> lý AI hỗ trợ nông nghiệp thông minh, tư vấn chọn nông sản tốt, tr<PERSON> chuyện và khám phá kiến thức mới!</p>
                <button
                    className="mt-4 px-8 py-3 bg-[#2EAF5D] hover:bg-[#238B4F] text-white font-semibold rounded-full text-lg shadow transition"
                    onClick={() => router.push("/login")}
                >
                    Bắt đầu
                </button>
            </div>
        </div>
    );
} 