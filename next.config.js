/** @type {import('next').NextConfig} */
const nextConfig = {
  // Tắt React StrictMode để tránh double API calls trong development
  reactStrictMode: false,

  // Add rewrites for API proxy if needed
  async rewrites() {
    return [
      {
        source: "/api/proxy/:path*",
        destination: "https://fchatai-api.salekit.com:3034/:path*",
      },
    ];
  },

  webpack: (config, { isServer }) => {
    // Fix for 'fs' module not found error on client side
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }
    return config;
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },

  // Đảm bảo server luôn chạy trên port 3000
  experimental: {
    serverComponentsExternalPackages: [],
  },
};

module.exports = nextConfig;
