import React from 'react';
import clsx from 'clsx';

interface LoadingProps {
    size?: number; // chiều dài, rộng của spinner
    color?: string; // màu viền spinner
    borderWidth?: number; // độ dày viền spinner
    className?: string; // thêm class tùy chỉnh
}

const Loading: React.FC<LoadingProps> = ({
    size = 32,
    color = 'gray-500',
    borderWidth = 2,
    className = '',
}) => {
    const spinnerSize = `${size}px`;
    const borderClass = `border-b-${borderWidth}`;
    const colorClass = `border-${color}`;

    return (
        <div
            className={clsx(
                'animate-spin rounded-full mx-auto',
                borderClass,
                colorClass,
                className
            )}
            style={{ height: spinnerSize, width: spinnerSize }}
        ></div>
    );
};

export default Loading;
