import { create } from 'zustand'

interface DomainConfig {
    domain: string;
    allowed: boolean;
    config?: any;
    originalDomain?: string;
    mappedDomain?: string;
    prompt_id?: string;
    // Không có token ở đây - token chỉ có khi đăng nhập thành công
}

interface AppState {
    promptId: string | null
    websiteId: string | null
    domainConfig: DomainConfig | null
    setPromptId: (id: string) => void
    setWebsiteId: (id: string) => void
    setDomainConfig: (config: DomainConfig | null) => void
    loadDomainFromCookie: () => boolean
    clearDomainVerification: () => void
}

export const useAppStore = create<AppState>((set) => ({
    promptId: null,
    websiteId: null,
    domainConfig: null,
    setPromptId: (id) => set({ promptId: id }),
    setWebsiteId: (id) => set({ websiteId: id }),

    loadDomainFromCookie: () => {
        // Không lưu trạng thái domain verification nữa
        // Luôn return false để xác thực lại domain
        console.log('🔄 Domain verification cache disabled - will verify again');
        return false;
    },

    clearDomainVerification: () => {
        console.log('🧹 Clearing domain verification');

        // Clear state only (không cần xóa cookie vì không lưu)
        set({
            domainConfig: null,
            promptId: null
        });
    },
    setDomainConfig: (config) => set((state) => {
        const newPromptId = config?.prompt_id || null;

        // Không lưu domain verification status vào cookie nữa
        console.log('✅ Domain config set (no persistence):', config);

        return {
            domainConfig: config,
            promptId: newPromptId
        };
    }),
}))
