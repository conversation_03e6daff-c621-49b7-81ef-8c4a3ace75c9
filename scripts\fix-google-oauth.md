# Hướng dẫn sửa lỗi Google OAuth "redirect_uri_mismatch"

## Nguyên nhân lỗi
Lỗi `redirect_uri_mismatch` x<PERSON>y ra khi URL redirect trong Google OAuth không khớp với URL đã cấu hình trong Google Cloud Console.

## <PERSON><PERSON><PERSON> bước sửa lỗi

### 1. <PERSON><PERSON>y cập Google Cloud Console
- Mở [Google Cloud Console](https://console.cloud.google.com/)
- Đ<PERSON><PERSON> nhập với tài khoản Google của bạn
- Chọn project hiện tại

### 2. Vào phần Credentials
- Trong menu bên trái, chọn "APIs & Services" → "Credentials"
- Tìm và click vào OAuth 2.0 Client ID của bạn

### 3. Thêm Authorized Redirect URIs
Thêm tất cả các URI sau vào phần "Authorized redirect URIs":

```
http://localhost:3000/api/auth/callback/google
http://localhost:3001/api/auth/callback/google
https://agent.trinhxuanthuy.id.vn/api/auth/callback/google
```

### 4. Lưu cấu hình
- Click "Save" để lưu thay đổi
- Đợi vài phút để Google cập nhật cấu hình

### 5. Kiểm tra lại
- Quay lại ứng dụng và thử đăng nhập Google lại
- Nếu vẫn lỗi, kiểm tra console log để xem URL redirect hiện tại

## Thông tin debug hiện tại

### Client ID
```
1037683057246-g38hdvnob34u3ghth264t5lbhd4ioo08.apps.googleusercontent.com
```

### Redirect URIs cần cấu hình
- `http://localhost:3000/api/auth/callback/google` (port 3000)
- `http://localhost:3001/api/auth/callback/google` (port 3001)
- `https://agent.trinhxuanthuy.id.vn/api/auth/callback/google` (production)

## Lưu ý quan trọng
- Ứng dụng hiện đang chạy trên port 3001 thay vì 3000
- Cần cấu hình cả hai port để tránh lỗi khi port thay đổi
- URL production cũng cần được thêm vào để deploy

## Kiểm tra debug
Truy cập `/debug/google-oauth` để xem thông tin chi tiết về cấu hình OAuth hiện tại.
