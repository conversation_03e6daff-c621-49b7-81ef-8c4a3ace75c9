import { create } from 'zustand'

type State = {
    count: number
}

type Actions = {
    increment: (qty: number) => void
    decrement: (qty: number) => void
}

const useExampleStore = create<State & Actions>((set) => ({
    count: 0,
    increment: (qty: number) => set((state) => ({ count: state.count + qty })),
    decrement: (qty: number) => set((state) => ({ count: state.count - qty })),
}))

export default useExampleStore;