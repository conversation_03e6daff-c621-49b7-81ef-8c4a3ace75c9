"use client";

import { ReactNode, useEffect, useState } from "react";
import { verifyDomain } from "@/services/authService";
import { useRouter } from "next/router";
import { useDomainStore } from "@/stores/domainStore";

interface DomainProviderProps {
  children: ReactNode;
}

export const DomainProvider = ({ children }: DomainProviderProps) => {
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const runVerify = async () => {
      try {
        let currentDomain = window.location.hostname;

        // Nếu là localhost thì dùng domain tạm để xác thực
        if (currentDomain === "localhost" || currentDomain === "127.0.0.1") {
          currentDomain = "agent.trinhxuanthuy.id.vn";
        }

        const data = await verifyDomain({ domain: currentDomain });
        if (!data) {
          throw new Error("Invalid domain data");
        }
        if (router.pathname !== "/welcome") {
          router.push("/welcome");
        }

      } catch (error) {
        console.error("Domain verification failed:", error);
      } finally {
        setLoading(false);
      }
    };

    runVerify();
  }, [router]);

  if (loading) return <div>Loading...</div>; // Hoặc spinner nếu cần

  return <>{children}</>;
};
