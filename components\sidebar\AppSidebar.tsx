"use client";

import { Plus, MessageSquare, MoreHorizontal, Edit, Trash2, <PERSON>t, Lightbulb, Zap, <PERSON>rkles } from 'lucide-react';
import React from 'react';
import { useState, useRef, useEffect } from 'react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarSeparator,
} from '@/components/ui/sidebar';






export function AppSidebar() {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const editInputRef = useRef<HTMLInputElement>(null);

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <img src="/favicon.ico" alt="Chat AI" width={32} height={32} className="rounded-full" />
          <span className="font-semibold text-lg">Chat AI</span>
        </div>
        <div className="flex flex-col gap-2 p-2">
          <div className="flex items-center gap-3 rounded-lg p-3 bg-muted/50">
            <div className="flex-shrink-0 rounded-full border border-amber-600 overflow-hidden size-8">
              <img src="/favicon.ico" alt="Chat AI" width={32} height={32} />
            </div>
            <div className="flex-1 overflow-hidden">
              <div className="flex items-center gap-1.5">
                <p className="truncate text-sm font-semibold">Chat AI</p>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-500">
                  <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                </svg>
              </div>
              <p className="truncate text-xs text-muted-foreground">Chào mừng đến với Chat AI</p>
            </div>
          </div>
        </div>
      </SidebarHeader>
      <SidebarSeparator />
      <SidebarContent className='mt-3'>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <button
                type="button"
                className='inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium rounded-2xl bg-blue-500 hover:bg-blue-600 py-2 text-white'>
                <Plus className="h-4 w-4" />
                <span>Bắt đầu chủ đề mới</span>
              </button>
              <SidebarGroupLabel className="text-blue-500">Chủ đề AI</SidebarGroupLabel>

              <SidebarMenuItem>
                <div className="flex items-center gap-2 px-3 py-3">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span className="text-sm text-white">Đang tải...</span>
                </div>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <div className="px-3 py-3 text-sm text-white/70">
                  Chưa có cuộc trò chuyện nào
                </div>
              </SidebarMenuItem>
              <SidebarMenuItem  >
                <SidebarMenuButton
                  className="w-full justify-between group"
                >
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <div
                        className="inline-flex items-center justify-center h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity rounded hover:bg-slate-600/50 cursor-pointer"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-3 w-3 text-white" />
                      </div>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                        }}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Sửa tên
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Xóa
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </SidebarMenuButton>
              </SidebarMenuItem>


            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
