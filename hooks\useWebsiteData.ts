import { useWebsiteStore } from '@/stores/websiteStore'

/**
 * Hook để lấy các thông tin cần thiết cho API calls
 */
export const useWebsiteData = () => {
    const {
        isVerified,
        promptId,
        websiteId,
        shopId,
        websiteInfo,
        websiteAuth
    } = useWebsiteStore()

    return {
        // Trạng thái verification
        isVerified,
        
        // IDs cho API calls
        promptId,      // prompt._id để call API khác
        websiteId,     // website.website_id để gửi request post google login
        shopId,        // shop_id nếu cần
        
        // Thông tin website để hiển thị
        websiteInfo,   // meta_title, meta_description, favicon, logo, thumbnail
        websiteAuth,   // google_login, client_id, client_secret, website_id
        
        // Helper functions
        isGoogleLoginEnabled: websiteAuth?.google_login === 2,
        hasWebsiteInfo: !!websiteInfo,
        hasAuthConfig: !!websiteAuth
    }
}

/**
 * Hook chỉ để lấy IDs cho API calls
 */
export const useApiIds = () => {
    const { promptId, websiteId, shopId, isVerified } = useWebsiteStore()
    
    return {
        promptId,
        websiteId, 
        shopId,
        isVerified,
        // Helper để kiểm tra có đủ thông tin để call API không
        canCallApi: isVerified && promptId && websiteId
    }
}

/**
 * Hook để lấy Google Login config
 */
export const useGoogleLoginConfig = () => {
    const { websiteAuth, websiteId, isVerified } = useWebsiteStore()
    
    return {
        isEnabled: websiteAuth?.google_login === 2,
        clientId: websiteAuth?.client_id,
        clientSecret: websiteAuth?.client_secret,
        websiteId: websiteId || websiteAuth?.website_id,
        isVerified,
        // Helper để kiểm tra có thể sử dụng Google Login không
        canUseGoogleLogin: isVerified && websiteAuth?.google_login === 2 && websiteAuth?.client_id
    }
}

/**
 * Hook để lấy thông tin website cho SEO/metadata
 */
export const useWebsiteSEO = () => {
    const { websiteInfo, isVerified } = useWebsiteStore()
    
    return {
        title: websiteInfo?.meta_title,
        description: websiteInfo?.meta_description,
        favicon: websiteInfo?.favicon,
        logo: websiteInfo?.logo,
        thumbnail: websiteInfo?.thumbnail,
        isVerified,
        // Helper để kiểm tra có thông tin SEO không
        hasSEOData: isVerified && websiteInfo
    }
}
