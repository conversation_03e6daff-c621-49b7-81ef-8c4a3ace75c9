import { useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { useWebsiteStore } from '@/stores/websiteStore';
import { loginWithGoogle, loginWithEmail, GoogleLoginPayload, EmailLoginPayload } from '@/services/authService';
import { decodeJwtPayload } from '@/helpers/encryptHelper';
import { toast } from 'sonner';

export const useAuth = () => {
    const {
        user,
        token,
        isAuthenticated,
        isLoading,
        setAuth,
        setLoading,
        logout,
        getAuthHeaders,
        loadTokenFromCookie
    } = useAuthStore();

    const { websiteId } = useWebsiteStore();

    // Load token từ cookie khi component mount
    useEffect(() => {
        if (!isAuthenticated && !token) {
            loadTokenFromCookie();
        }
    }, [isAuthenticated, token, loadTokenFromCookie]);

    // Google Login
    const loginGoogle = async (googleData: {
        email: string;
        name: string;
        avatar?: string;
        google_id: string;
        ref?: string;
    }) => {
        if (!websiteId) {
            throw new Error('Website ID không tồn tại. Vui lòng thử lại.');
        }

        setLoading(true);
        try {
            const payload: GoogleLoginPayload = {
                email: googleData.email,
                name: googleData.name,
                avatar: googleData.avatar || '',
                website_id: websiteId,
                ref: googleData.ref || 'thuytx',
                google_id: googleData.google_id
            };

            const response = await loginWithGoogle(payload);

            if (response.error === false && response.status === 200) {
                console.log('✅ Google login successful, saving token to cookie');

                // Xử lý response data
                if (response.data && typeof response.data === 'object') {
                    // Data đã được giải mã - có thông tin user và token
                    const userData = response.data;
                    const token = userData.token || '';

                    if (token) {
                        setAuth(userData, token);
                        toast.success(response.msg || 'Đăng nhập Google thành công!');
                        return { success: true, user: userData };
                    }
                } else if (response.data && typeof response.data === 'string') {
                    // Token string - lưu token và thông tin cơ bản
                    const token = response.data;
                    const userData = {
                        _id: '',
                        email: googleData.email,
                        name: googleData.name,
                        avatar: googleData.avatar
                    };

                    setAuth(userData, token);
                    toast.success(response.msg || 'Đăng nhập Google thành công!');
                    return { success: true, user: userData, token };
                }
            }

            throw new Error(response.msg || 'Đăng nhập Google thất bại');
        } catch (error: any) {
            console.error('❌ Google login error:', error);
            toast.error(error.message || 'Có lỗi xảy ra khi đăng nhập Google');
            throw error;
        } finally {
            setLoading(false);
        }
    };

    // Email/Password Login
    const loginEmail = async (credentials: EmailLoginPayload) => {
        setLoading(true);
        try {
            const response = await loginWithEmail(credentials);

            if (response.error === false && response.status === 200) {
                console.log('✅ Email login successful, saving token to cookie');

                // Xử lý response data
                if (response.data && typeof response.data === 'object') {
                    // Data đã được giải mã - có thông tin user và token
                    const userData = response.data;
                } else if (response.data && typeof response.data === 'string') {
                    // Token string - lưu token và thông tin cơ bản
                    const token = response.data;
                    // Giải mã JWT lấy user_id, shop_id
                    const payload = decodeJwtPayload(token);
                    console.log("login giải mã: ", payload)
                    const userData = {
                        _id: payload?.user_id || '',
                        shop_id: payload?.shop_id,
                        email: credentials.email,
                        name: credentials.email.split('@')[0]
                    };

                    setAuth(userData, token);
                    toast.success(response.msg || 'Đăng nhập thành công!');
                    return { success: true, user: userData, token };
                }
            }

            throw new Error(response.msg || 'Email hoặc mật khẩu không đúng');
        } catch (error: any) {
            console.error('❌ Email login error:', error);
            toast.error(error.message || 'Có lỗi xảy ra khi đăng nhập');
            throw error;
        } finally {
            setLoading(false);
        }
    };

    return {
        // State
        user,
        token,
        isAuthenticated,
        isLoading,

        // Actions
        loginGoogle,
        loginEmail,
        logout,

        // Helpers
        getAuthHeaders,
        hasValidAuth: isAuthenticated && !!token,
    };
};

export default useAuth;
