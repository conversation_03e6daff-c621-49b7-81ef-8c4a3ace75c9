'use client';
import React, { useEffect, useRef } from "react";
import CryptoJ<PERSON> from "crypto-js";
import { dataImageType } from "@/types/photo";
import { X } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { getTokenInfo } from "@/helpers/tokenHelper";

interface ImagePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onImageSelect: (dataImages: dataImageType) => void;
}

const ImagePopup: React.FC<ImagePopupProps> = ({
  isOpen,
  onClose,
  onImageSelect,
}) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const { token } = useAuth();

  // Lấy thông tin từ token
  const tokenInfo = token ? getTokenInfo(token) : null;
  const shopId = tokenInfo?.shopId || '';

  // Sử dụng package_id mặc định hoặc từ env
  const packageId = process.env.NEXT_PUBLIC_PACKAGE_ID || "1";
  const servicepackages = "fchat" + packageId;
  const combinedString = `https://fchat.vn/_${shopId}_${CryptoJS.MD5(
    servicepackages
  ).toString()}_${token || ''}`;
  const base64Encoded = btoa(combinedString);
  const urlEncoded = encodeURIComponent(base64Encoded);

  useEffect(() => {
    const iframeElement = document.getElementById(
      "iframe_photo"
    ) as HTMLIFrameElement | null;
    const openedWindow = iframeElement?.contentWindow;

    if (isOpen && openedWindow && shopId && token) {
      intervalRef.current = setInterval(() => {
        openedWindow.postMessage(
          "fchat_" + shopId,
          "https://media.salekit.com?token=" + urlEncoded
        );
      }, 300);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isOpen, shopId, token, urlEncoded]);

  const receiveMessage = (event: MessageEvent) => {
    if (event.data && event.data.domain === "photo.salekit.com") {
      switch (event.data.status) {
        case "success":
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          break;
        case "close":
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          break;
        default:
          onImageSelect(event.data);
          onClose();
      }

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
  };

  useEffect(() => {
    if (isOpen) {
      window.addEventListener("message", receiveMessage, false);
    }
    return () => {
      window.removeEventListener("message", receiveMessage, false);
    };
  }, [isOpen]);

  return isOpen ? (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="bg-white rounded shadow-lg shop-images">
        <div className="shop-images-title">
          <div className="flex">
            <h2 className="text-lg font-bold ml-2">Ảnh của shop</h2>
          </div>
          <button
            type="button"
            aria-label="Đóng"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        <iframe
          id="iframe_photo"
          title="Ảnh của shop"
          src="https://media.salekit.com"
          className="w-full h-96 border"
        />
      </div>
      <style>
        {`
                .shop-images {
                    max-width: 100%;
                    width: 990px;
                    min-width: 600px;
                    margin: auto;
                    z-index: 100;
                    border-radius: 10px;
                }
                .shop-images-title {
                    display: inline-flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    background: #ffffff;
                    border-radius: 10px 10px 0 0;
                    padding: 10px 12px;
                }
                #iframe_photo {
                    position: relative;
                    border-bottom-left-radius: 10px;
                    border-bottom-right-radius: 10px;
                    height: 508px;
                }
                `}
      </style>
    </div>
  ) : null;
};

export default ImagePopup;
