interface LoginCredentials {
  email: string
  password: string
}

interface AuthData {
  token: string
  user?: any
}

// Mock login function - replace with actual API call
export const loginWithEmail = async (credentials: LoginCredentials) => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000))

  // Mock response
  return {
    success: true,
    token: 'mock-jwt-token',
    user: {
      email: credentials.email,
      name: 'User Name'
    }
  }
}

// Mock auth store - replace with actual auth store
export const setAuthData = (data: AuthData) => {
  // Store in localStorage for now
  localStorage.setItem('auth_token', data.token)
  if (data.user) {
    localStorage.setItem('auth_user', JSON.stringify(data.user))
  }
}

export const getAuthData = (): AuthData | null => {
  // Check if running in browser
  if (typeof window === 'undefined') return null

  const token = localStorage.getItem('auth_token')
  const userStr = localStorage.getItem('auth_user')

  if (!token) return null

  return {
    token,
    user: userStr ? JSON.parse(userStr) : null
  }
}

export const clearAuthData = () => {
  if (typeof window === 'undefined') return

  localStorage.removeItem('auth_token')
  localStorage.removeItem('auth_user')
}
