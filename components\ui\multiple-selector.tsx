"use client"

import * as React from "react"
import { Check, ChevronsUpDown, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

export type Option = {
  name: string
  _id: string
  category?: string
}

interface MultiSelectProps {
  options: Option[]
  selected: Option[]
  onChange: (options: Option[]) => void
  placeholder?: string
}

export function MultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select items...",
  ...props
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false)

  const handleUnselect = (option: Option) => {
    onChange(selected.filter((item) => item._id !== option._id))
  }

  const groupedOptions = options.reduce(
    (acc, option) => {
      const category = option.category || ""
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(option)
      return acc
    },
    {} as Record<string, Option[]>,
  )

  return (
    <Popover open={open} onOpenChange={setOpen} {...props} >
      <PopoverTrigger asChild>
        <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between h-auto hover:bg-white">
          <div className="flex gap-1 flex-wrap overflow-x-auto items-center">
            {selected.length > 0 ? (
              selected.map((option, index) => (
                <Badge key={index + 1} variant="secondary" className="mr-1 mb-1">
                  {option.name}
                  <div
                    className="ml-1 rounded-full outline-none"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleUnselect(option)
                      }
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                    }}
                    onClick={() => handleUnselect(option)}
                  >
                    <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                  </div>
                </Badge>
              ))
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0 overflow-y-auto">
        <Command>
          <CommandInput placeholder="Tìm kiếm..." />
          <CommandList className="max-h-[300px] overflow-y-auto">
            <CommandEmpty>No item found.</CommandEmpty>
            {Object.entries(groupedOptions).map(([category, categoryOptions], index) => (
              <React.Fragment key={category}>
                {index > 0 && <CommandSeparator />}
                <CommandGroup heading={category}>
                  {categoryOptions.map((option) => {
                    const isSelected = selected.some((item) => item._id === option._id)
                    return (
                      <CommandItem
                        key={option._id}
                        onSelect={() => {
                          onChange(
                            isSelected ? selected.filter((item) => item._id !== option._id) : [...selected, option],
                          )
                        }}
                      >
                        <Check className={cn("mr-2 h-4 w-4", isSelected ? "opacity-100" : "opacity-0")} />
                        {option.name}
                      </CommandItem>
                    )
                  })}
                </CommandGroup>
              </React.Fragment>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

