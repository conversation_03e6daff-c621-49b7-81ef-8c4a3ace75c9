'use client'

import { ArrowLeft } from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo'

export const WebsiteHeader = () => {
  const router = useRouter()
  const { websiteInfo } = useWebsiteInfo()

  return (
    <div className="text-center mb-8">
      {/* Back Button */}
      <button
        type="button"
        onClick={() => router.back()}
        className="inline-flex items-center text-gray-600 hover:text-gray-800 mb-6 transition-colors"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Quay lại
      </button>

      {/* Logo */}
      {websiteInfo?.logo && (
        <div className="mb-4">
          <Image
            src={websiteInfo.logo}
            alt="Logo"
            width={80}
            height={80}
            className="mx-auto rounded-lg"
          />
        </div>
      )}

      {/* Title */}
      <h1 className="text-3xl font-bold text-gray-900 mb-2">
        {websiteInfo?.meta_title || 'Đăng nhập'}
      </h1>

      {/* Description */}
      {websiteInfo?.meta_description && (
        <p className="text-gray-600 text-sm">
          {websiteInfo.meta_description}
        </p>
      )}
    </div>
  )
}
