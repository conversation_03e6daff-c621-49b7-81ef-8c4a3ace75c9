
import { EP, QUERY } from "@/configs/constants/api";
import { getApiEndpoint } from "@/helpers/handleApiUrl";
import axiosClient from "@/lib/axios";
import { DataResponseType } from "@/types";
export interface PayloadUpdateConversation {
    id: string;
    name: string;
}
export const updateConversation = async<T>(payload: PayloadUpdateConversation) => {
    const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL)
        .post<DataResponseType<T>>(getApiEndpoint([EP.API, EP.V1, EP.CONVERSATION, EP.UPDATE]), payload);
    return data;
}