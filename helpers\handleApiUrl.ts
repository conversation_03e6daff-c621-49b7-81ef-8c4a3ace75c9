/**
 * <PERSON><PERSON><PERSON> này dùng để lấy endpoint khi truyền vào
 * VD: getApiEndPoint(['api','v1','example']) => /api/v1/exmple
 * VD với endpoint có query: getApiEndPoint(['api','v1','example'], {page: 1}) => /api/v1/exmple/?page=1
 */
export function getApiEndpoint(path: string[], query?: Record<string, any>): string {
    const fullPath = path.join('/');
    if (query) {
        const queryString = new URLSearchParams(query).toString();
        return `/${fullPath}?${queryString}`;
    }
    return `/${fullPath}`;
}