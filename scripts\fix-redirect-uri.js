#!/usr/bin/env node

/**
 * Script hướng dẫn sửa lỗi redirect_uri_mismatch
 */

console.log('🚨 GOOGLE OAUTH REDIRECT URI MISMATCH - HƯỚNG DẪN SỬA');
console.log('=====================================================\n');

console.log('❌ LỖI HIỆN TẠI: redirect_uri_mismatch');
console.log('Ứng dụng đang sử dụng: http://localhost:3000/api/auth/callback/google');
console.log('Nhưng Google Console chưa có URI này!\n');

console.log('🔧 CÁCH SỬA (QUAN TRỌNG):');
console.log('1. Truy cập: https://console.cloud.google.com/');
console.log('2. Chọn project của bạn');
console.log('3. Vào "APIs & Services" → "Credentials"');
console.log('4. Click vào OAuth 2.0 Client ID: 1037683057246-g38hdvnob34u3ghth264t5lbhd4ioo08.apps.googleusercontent.com');
console.log('5. Trong phần "Authorized redirect URIs", THÊM URI này:');
console.log('   📌 http://localhost:3000/api/auth/callback/google');
console.log('6. Click "SAVE" để lưu');
console.log('7. Đợi 1-2 phút để thay đổi có hiệu lực');
console.log('8. Thử đăng nhập lại\n');

console.log('📋 DANH SÁCH ĐẦY ĐỦ CẦN THÊM:');
console.log('');
console.log('🌐 Authorized JavaScript origins:');
console.log('   - http://localhost:3000');
console.log('   - http://localhost:3001');
console.log('   - https://agent.trinhxuanthuy.id.vn');
console.log('');
console.log('🔗 Authorized redirect URIs:');
console.log('   - http://localhost:3000/api/auth/callback/google  ← QUAN TRỌNG NHẤT');
console.log('   - http://localhost:3001/api/auth/callback/google');
console.log('   - https://agent.trinhxuanthuy.id.vn/api/auth/callback/google');
console.log('');

console.log('📝 HƯỚNG DẪN CHI TIẾT:');
console.log('1. Mở Google Cloud Console');
console.log('2. Chọn project đúng');
console.log('3. Vào APIs & Services → Credentials');
console.log('4. Click vào OAuth 2.0 Client ID');
console.log('5. Scroll xuống phần "Authorized redirect URIs"');
console.log('6. Click "ADD URI"');
console.log('7. Paste: http://localhost:3000/api/auth/callback/google');
console.log('8. Click "SAVE"');
console.log('9. Đợi 1-2 phút');
console.log('10. Test lại Google OAuth');
console.log('');

console.log('⚠️ LƯU Ý:');
console.log('- Phải thêm CHÍNH XÁC URI: http://localhost:3000/api/auth/callback/google');
console.log('- Không được có dấu cách hoặc ký tự thừa');
console.log('- Sau khi lưu, đợi 1-2 phút để Google cập nhật');
console.log('- Nếu vẫn lỗi, thử clear cache browser và thử lại');
console.log('');

console.log('🚀 Sau khi cập nhật xong, test tại: http://localhost:3000/login');
