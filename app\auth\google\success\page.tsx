"use client"

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import { Loader2 } from 'lucide-react'

export default function GoogleSuccessPage() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing')

    useEffect(() => {
        const handleGoogleLogin = async () => {
            try {
                const token = searchParams.get('token')
                const message = searchParams.get('message')
                const userDataParam = searchParams.get('user_data')

                if (!token) {
                    throw new Error('Không nhận được token từ server')
                }

                if (!userDataParam) {
                    throw new Error('Không nhận được thông tin user từ Google')
                }

                const userData = JSON.parse(userDataParam)
                console.log('🔍 Processing Google login with token and user data:', {
                    hasToken: !!token,
                    userData
                })

                // Lưu token vào cookie
                const isProduction = process.env.NODE_ENV === 'production'
                document.cookie = `fchatai_token=${token}; path=/; max-age=${7 * 24 * 60 * 60}; ${isProduction ? 'secure;' : ''} samesite=strict`

                // Cập nhật auth store
                const { useAuthStore } = await import('@/stores/authStore')
                const { setAuth } = useAuthStore.getState()

                setAuth({
                    _id: userData.google_id,
                    email: userData.email,
                    name: userData.name,
                    avatar: userData.avatar || ''
                }, token)

                setStatus('success')
                toast.success(message || 'Đăng nhập Google thành công!')

                // Redirect về trang chủ sau 1 giây
                setTimeout(() => {
                    router.push('/')
                }, 1000)

            } catch (error: any) {
                console.error('❌ Google login error:', error)
                setStatus('error')
                toast.error(error.message || 'Có lỗi xảy ra khi đăng nhập Google')

                // Redirect về trang login sau 3 giây
                setTimeout(() => {
                    router.push('/login')
                }, 3000)
            }
        }

        handleGoogleLogin()
    }, [searchParams, router])

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="text-center space-y-6 max-w-md mx-auto">
                <div className="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-xl">
                    {status === 'processing' && (
                        <Loader2 className="w-10 h-10 text-blue-600 animate-spin" />
                    )}
                    {status === 'success' && (
                        <div className="w-10 h-10 text-green-600">✅</div>
                    )}
                    {status === 'error' && (
                        <div className="w-10 h-10 text-red-600">❌</div>
                    )}
                </div>

                <div className="space-y-2">
                    {status === 'processing' && (
                        <>
                            <h2 className="text-2xl font-bold text-gray-900">
                                Đang xử lý đăng nhập Google
                            </h2>
                            <p className="text-gray-600">
                                Vui lòng đợi trong giây lát...
                            </p>
                        </>
                    )}
                    {status === 'success' && (
                        <>
                            <h2 className="text-2xl font-bold text-green-900">
                                Đăng nhập thành công!
                            </h2>
                            <p className="text-green-600">
                                Đang chuyển hướng về trang chủ...
                            </p>
                        </>
                    )}
                    {status === 'error' && (
                        <>
                            <h2 className="text-2xl font-bold text-red-900">
                                Đăng nhập thất bại
                            </h2>
                            <p className="text-red-600">
                                Đang chuyển hướng về trang đăng nhập...
                            </p>
                        </>
                    )}
                </div>

                {status === 'processing' && (
                    <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                        <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                        <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse [animation-delay:0.2s]"></div>
                        <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse [animation-delay:0.4s]"></div>
                    </div>
                )}
            </div>
        </div>
    )
}
