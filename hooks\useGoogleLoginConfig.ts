import { useWebsiteStore } from '@/stores/websiteStore'

export const useGoogleLoginConfig = () => {
  const { websiteAuth, isVerified, isLoading } = useWebsiteStore()

  console.log('🔍 useGoogleLoginConfig debug:', {
    websiteAuth,
    isVerified,
    isLoading,
    hasWebsiteAuth: !!websiteAuth,
    googleLogin: websiteAuth?.google_login,
    clientId: websiteAuth?.client_id,
    websiteId: websiteAuth?.website_id
  });

  // Nếu đang loading, chờ
  if (isLoading) {
    console.log('⏳ Domain verification still loading...');
    return null;
  }

  // Nếu không có websiteAuth sau khi verify xong, dùng fallback từ env
  if (!websiteAuth?.google_login || !websiteAuth.client_id) {
    console.log('❌ Google login config not available from API, checking fallback...');

    // Fallback config từ environment variables
    const fallbackClientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
    const fallbackWebsiteId = process.env.NEXT_PUBLIC_WEBSITE_ID;

    if (fallbackClientId && fallbackWebsiteId) {
      console.log('✅ Using fallback Google config from env');
      return {
        clientId: fallbackClientId,
        clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET || '',
        enabled: 2, // Enable Google login
        websiteId: fallbackWebsiteId
      };
    }

    console.log('❌ No Google login config available (API or fallback)');
    return null;
  }

  const config = {
    clientId: websiteAuth.client_id,
    clientSecret: websiteAuth.client_secret,
    enabled: websiteAuth.google_login,
    websiteId: websiteAuth.website_id
  };

  console.log('✅ Google login config available from API:', config);
  return config;
}
