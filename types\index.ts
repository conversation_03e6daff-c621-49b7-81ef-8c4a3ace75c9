

//Viết type chung phạm vi global ở đây
export type DataResponseType<T> = {
    error?: boolean
    success: boolean
    msg?: string
    data: T
    status: number
    message: string
}
export interface ApiError {
    success: false
    message: string
    error: string
    statusCode: number
}

export interface PaginationParams {
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: "asc" | "desc"
}

export interface PaginatedResponse<T> extends DataResponseType<T[]> {
    pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
    }
}