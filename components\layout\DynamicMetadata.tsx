'use client'

import { useEffect } from 'react'
import { useVerifiedWebsiteInfo } from '@/hooks/useDomainVerification'

export const DynamicMetadata = () => {
    const { isVerified, websiteInfo } = useVerifiedWebsiteInfo()

    useEffect(() => {
        if (isVerified && websiteInfo) {
            // Cập nhật title
            if (websiteInfo.meta_title) {
                document.title = websiteInfo.meta_title
            }

            // Cập nhật meta description
            if (websiteInfo.meta_description) {
                let metaDescription = document.querySelector('meta[name="description"]')
                if (!metaDescription) {
                    metaDescription = document.createElement('meta')
                    metaDescription.setAttribute('name', 'description')
                    document.head.appendChild(metaDescription)
                }
                metaDescription.setAttribute('content', websiteInfo.meta_description)
            }

            // Cập nhật favicon
            if (websiteInfo.favicon) {
                let favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
                if (!favicon) {
                    favicon = document.createElement('link')
                    favicon.rel = 'icon'
                    document.head.appendChild(favicon)
                }
                favicon.href = websiteInfo.favicon
            }

            console.log('✅ Dynamic metadata updated:', {
                title: websiteInfo.meta_title,
                description: websiteInfo.meta_description,
                favicon: websiteInfo.favicon
            })
        }
    }, [isVerified, websiteInfo])

    return null // Component này chỉ để cập nhật metadata, không render gì
}
