// Message interfaces
export interface Message {
    _id: string;
    type: number; // 0: AI response, 1: User message
    content: string;
    albums: any[];
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    conversation_id: string;
    created_at: string;
    updated_at: string;
    __v: number;
}

export interface MessageListResponse {
    error: boolean;
    status: number;
    msg: string;
    data: Message[];
}