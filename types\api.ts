// API Response Types

// User Profile API Types
export interface ApiUser {
  _id: string;
  name: string;
  email: string;
  username: string;
  email_verify: number;
  google_id?: string;
  website_id: string;
  ref: string | null;
  status: number;
}

export interface ApiShop {
  _id: string;
  website_id: string;
  name: string;
  email: string;
  username: string;
  ref: string | null;
  api_token: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface ApiPackage {
  _id: string;
  shop_id: string;
  package_id: number;
  month: number;
  agent: number;
  agent_used: number;
  capacity: number;
  credit: number;
  credit_use: number;
  credits_bonus: number;
  credits_bonus_used: number;
  package_system_id: number;
  expired_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface UserProfileData {
  user: ApiUser;
  shop: ApiShop;
  package: ApiPackage;
}

export interface UserProfileResponse {
  error: boolean;
  status: number;
  msg: string;
  data: UserProfileData;
}

// Auth Types
export interface AuthUser {
  _id: string;
  name: string;
  email: string;
  avatar?: string;
  shop_id?: string;
  website_id?: string;
}

export interface LoginResponse {
  error: boolean;
  status: number;
  msg: string;
  data: string | AuthUser; // Token string hoặc user object
}

// Domain Verification Types
export interface DomainConfig {
  domain: string;
  allowed: boolean;
  config?: any;
  originalDomain?: string;
  mappedDomain?: string;
  prompt_id?: string;
}

// Common API Response
export interface ApiResponse<T = any> {
  error: boolean;
  status: number;
  msg: string;
  data: T;
}
