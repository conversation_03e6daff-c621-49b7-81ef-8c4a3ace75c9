#!/usr/bin/env node

/**
 * Script để test Google OAuth flow
 */

console.log('🧪 GOOGLE OAUTH FLOW TEST');
console.log('=========================\n');

console.log('📋 FLOW HIỆN TẠI:');
console.log('1. User click "<PERSON><PERSON><PERSON> nhập Google" tại /login');
console.log('2. Redirect đến Google OAuth với redirect_uri: http://localhost:3000/api/auth/callback/google');
console.log('3. Google redirect về /api/auth/callback/google với code');
console.log('4. Server exchange code for access_token');
console.log('5. Server lấy user info từ Google');
console.log('6. Server gọi API backend: POST /api/v1/user/login/google');
console.log('7. Backend trả về: { error: false, status: 200, msg: "...", data: "token" }');
console.log('8. Redirect đến /auth/google/success với token trong URL');
console.log('9. Success page lưu token vào cookie và cập nhật auth store');
console.log('10. Redirect về trang chủ');
console.log('');

console.log('🔧 CẤU HÌNH HIỆN TẠI:');
console.log('- App URL: http://localhost:3000');
console.log('- Redirect URI: http://localhost:3000/api/auth/callback/google');
console.log('- Backend API: https://fchatai-api.salekit.com:3034');
console.log('- Website ID: 684296d93545562647c5f213');
console.log('');

console.log('✅ KIỂM TRA:');
console.log('1. Mở http://localhost:3000/login');
console.log('2. Click nút "Đăng nhập Google"');
console.log('3. Hoàn thành OAuth flow với Google');
console.log('4. Kiểm tra console log để xem response từ backend');
console.log('5. Kiểm tra cookie fchatai_token đã được lưu');
console.log('6. Kiểm tra auth state trong ứng dụng');
console.log('');

console.log('🐛 NẾU GẶP LỖI:');
console.log('- "redirect_uri_mismatch": Kiểm tra Google Console có đúng URI không');
console.log('- "Failed to exchange code": Kiểm tra client_secret và redirect_uri');
console.log('- "Backend API failed": Kiểm tra endpoint và payload gửi đến backend');
console.log('- "Token not saved": Kiểm tra success page và cookie logic');
console.log('');

console.log('🚀 Sẵn sàng test!');
