import { create } from 'zustand';

interface User {
    _id: string;
    email: string;
    name: string;
    avatar?: string;
    shop_id?: string;
    website_id?: string;
}

interface AuthState {
    user: User | null;
    token: string | null;
    isAuthenticated: boolean;
    isLoading: boolean;

    // Actions
    setAuth: (user: User, token: string) => void;
    setUser: (user: User) => void;
    setToken: (token: string) => void;
    setLoading: (loading: boolean) => void;
    logout: () => void;

    // Helper methods
    getAuthHeaders: () => { Authorization: string } | {};
    loadTokenFromCookie: () => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,

    setAuth: (user, token) => {
        console.log('🔐 Setting auth data after successful login:', {
            user: user.email,
            hasToken: !!token
        });

        // Lưu token vào cookie khi đăng nhập thành công
        if (token && typeof window !== 'undefined') {
            // Lưu vào cookie với thời hạn 7 ngày, chỉ secure trong production
            const isProduction = process.env.NODE_ENV === 'production';
            document.cookie = `fchatai_token=${token}; path=/; max-age=${7 * 24 * 60 * 60}; ${isProduction ? 'secure;' : ''} samesite=strict`;
        }

        set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false
        });
    },

    setUser: (user) => {
        console.log('👤 Setting user:', user.email);
        set({ user, isAuthenticated: !!user });
    },

    setToken: (token) => {
        console.log('🎫 Setting token:', !!token);
        set({ token });
    },

    setLoading: (loading) => set({ isLoading: loading }),

    logout: () => {
        console.log('🚪 Logging out user');

        // Chỉ xóa auth token, giữ domain verification
        if (typeof window !== 'undefined') {
            document.cookie = 'fchatai_token=; path=/; max-age=0';
            console.log('🍪 Auth token cleared');
        }

        set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
        });
    },

    getAuthHeaders: () => {
        const { token } = get();
        return token ? { Authorization: `Bearer ${token}` } : {};
    },

    loadTokenFromCookie: () => {
        if (typeof window !== 'undefined') {
            const cookies = document.cookie.split(';');
            const tokenCookie = cookies.find(cookie =>
                cookie.trim().startsWith('fchatai_token=')
            );

            if (tokenCookie) {
                const token = tokenCookie.split('=')[1];
                if (token && token !== 'undefined') {
                    console.log('🍪 Loading token from cookie');
                    set({
                        token,
                        isAuthenticated: true
                    });
                }
            }
        }
    }
}));
