import axios from "axios";
import { setCookie } from "cookies-next/client";

export const getUserProfile = async (token: string) => {
    const res = await axios.get("https://fchatai-api.salekit.com:3034/api/v1/user/profile", {
        headers: {
            Authorization: `<PERSON><PERSON> ${token}`,
        },
    });
    const data = res.data?.data;
    if (data?.user?._id) setCookie("user_id", data.user._id);
    if (data?.shop?._id) setCookie("shop_id", data.shop._id);
    return data;
};
