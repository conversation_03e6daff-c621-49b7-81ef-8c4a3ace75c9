"use client"

import React, { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useAppStore } from '@/stores/appStore'
import { Button } from '@/components/ui/button'
import { LogOut, Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

interface LogoutButtonProps {
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
    size?: 'default' | 'sm' | 'lg' | 'icon'
    className?: string
    showIcon?: boolean
    children?: React.ReactNode
}

export function LogoutButton({
    variant = 'outline',
    size = 'default',
    className = '',
    showIcon = true,
    children
}: LogoutButtonProps) {
    const { logout, isAuthenticated } = useAuth()
    const [isLoggingOut, setIsLoggingOut] = useState(false)
    const router = useRouter()

    const handleLogout = async () => {
        try {
            setIsLoggingOut(true)

            // Chỉ logout user, giữ domain verification
            logout()

            toast.success('Đăng xuất thành công!')

            // Redirect về trang welcome sau khi logout
            setTimeout(() => {
                router.push('/welcome')
            }, 1000)

        } catch (error) {
            console.error('Logout error:', error)
            toast.error('Có lỗi xảy ra khi đăng xuất')
        } finally {
            setIsLoggingOut(false)
        }
    }

    // Không hiển thị nếu chưa đăng nhập
    if (!isAuthenticated) {
        return null
    }

    return (
        <Button
            variant={variant}
            size={size}
            className={className}
            onClick={handleLogout}
            disabled={isLoggingOut}
        >
            {isLoggingOut ? (
                <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
                showIcon && <LogOut className="w-4 h-4" />
            )}
            {children || (
                <span className={showIcon ? 'ml-2' : ''}>
                    {isLoggingOut ? 'Đang đăng xuất...' : 'Đăng xuất'}
                </span>
            )}
        </Button>
    )
}
