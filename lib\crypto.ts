import CryptoJS from 'crypto-js'

const SECRET_KEY = process.env.NEXT_PUBLIC_CRYPTOJS_SECRET_KEY || "ultramailerCryptoTokenTrackEmail"

export function decryptAesCBC(encryptedBase64: string, secretKey: string = SECRET_KEY): string | null {
  try {
    console.log('🔐 Attempting to decrypt data with CryptoJS');

    const iv = CryptoJS.enc.Utf8.parse(secretKey.substring(0, 16));
    const key = CryptoJS.enc.Utf8.parse(secretKey);

    const decrypted = CryptoJS.AES.decrypt(encryptedBase64, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);

    if (decryptedText && decryptedText.length > 0) {
      console.log('✅ Decryption successful');
      return decryptedText;
    }

    console.log('❌ Decryption failed - empty result');
    return null;
  }
  catch (err) {
    console.error('❌ Giải mã AES thất bại:', err);
    return null;
  }
}



export function encryptAesCBC(data: string, key: string = SECRET_KEY): string {
  try {
    // Generate random IV
    const iv = CryptoJS.lib.WordArray.random(16)

    // Encrypt
    const encrypted = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(key), {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })

    // Combine IV + encrypted data and encode as base64
    const combined = iv.concat(encrypted.ciphertext)
    return CryptoJS.enc.Base64.stringify(combined)
  } catch (error) {
    console.error('Encryption error:', error)
    return ''
  }
}
