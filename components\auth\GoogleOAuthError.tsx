"use client"

import { useState } from 'react'
import { Al<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ExternalLink, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'
import { getGoogleRedirectUri } from '@/lib/googleAuth'

interface GoogleOAuthErrorProps {
    error?: string
    onRetry?: () => void
}

export default function GoogleOAuthError({ error, onRetry }: GoogleOAuthErrorProps) {
    const [showDetails, setShowDetails] = useState(false)
    
    const redirectUri = getGoogleRedirectUri()
    const baseUrl = typeof window !== 'undefined' 
        ? `${window.location.protocol}//${window.location.host}` 
        : 'http://localhost:3001'

    const requiredUris = [
        'http://localhost:3000/api/auth/callback/google',
        'http://localhost:3001/api/auth/callback/google',
        'https://agent.trinhxuanthuy.id.vn/api/auth/callback/google'
    ]

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text)
        toast.success('Đã copy vào clipboard!')
    }

    const isRedirectUriError = error === 'redirect_uri_mismatch' || error?.includes('redirect_uri')

    if (!isRedirectUriError && !error) return null

    return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                    <h3 className="font-medium text-red-800 mb-2">
                        Lỗi đăng nhập Google
                    </h3>
                    
                    {isRedirectUriError ? (
                        <div className="space-y-3">
                            <p className="text-red-700 text-sm">
                                URL redirect không khớp với cấu hình trong Google Console. 
                                Vui lòng cấu hình lại theo hướng dẫn bên dưới.
                            </p>
                            
                            <div className="bg-white border border-red-200 rounded p-3">
                                <p className="text-sm font-medium text-red-800 mb-2">
                                    URL hiện tại:
                                </p>
                                <div className="flex items-center gap-2">
                                    <code className="bg-red-100 px-2 py-1 rounded text-xs flex-1 font-mono">
                                        {redirectUri}
                                    </code>
                                    <button
                                        type="button"
                                        onClick={() => copyToClipboard(redirectUri)}
                                        className="text-red-600 hover:text-red-800"
                                        title="Copy URL"
                                    >
                                        <Copy className="w-4 h-4" />
                                    </button>
                                </div>
                            </div>

                            <button
                                type="button"
                                onClick={() => setShowDetails(!showDetails)}
                                className="text-red-600 hover:text-red-800 text-sm font-medium"
                            >
                                {showDetails ? 'Ẩn' : 'Hiện'} hướng dẫn sửa lỗi
                            </button>

                            {showDetails && (
                                <div className="bg-white border border-red-200 rounded p-4 space-y-3">
                                    <h4 className="font-medium text-red-800">
                                        Cách sửa lỗi:
                                    </h4>
                                    
                                    <ol className="text-sm text-red-700 space-y-2 list-decimal list-inside">
                                        <li>
                                            Truy cập{' '}
                                            <a 
                                                href="https://console.cloud.google.com/apis/credentials" 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:underline inline-flex items-center gap-1"
                                            >
                                                Google Cloud Console
                                                <ExternalLink className="w-3 h-3" />
                                            </a>
                                        </li>
                                        <li>Chọn OAuth 2.0 Client ID của bạn</li>
                                        <li>Thêm các URI sau vào "Authorized redirect URIs":</li>
                                    </ol>

                                    <div className="space-y-2">
                                        {requiredUris.map((uri, index) => (
                                            <div key={index} className="flex items-center gap-2 bg-gray-50 p-2 rounded">
                                                <code className="text-xs font-mono flex-1">
                                                    {uri}
                                                </code>
                                                <button
                                                    type="button"
                                                    onClick={() => copyToClipboard(uri)}
                                                    className="text-blue-600 hover:text-blue-800"
                                                    title="Copy URI"
                                                >
                                                    <Copy className="w-3 h-3" />
                                                </button>
                                            </div>
                                        ))}
                                    </div>

                                    <p className="text-xs text-red-600">
                                        💡 Sau khi thêm, click "Save" và đợi vài phút để Google cập nhật cấu hình.
                                    </p>
                                </div>
                            )}
                        </div>
                    ) : (
                        <p className="text-red-700 text-sm">
                            {error || 'Có lỗi xảy ra khi đăng nhập với Google. Vui lòng thử lại.'}
                        </p>
                    )}

                    <div className="flex gap-2 mt-4">
                        {onRetry && (
                            <button
                                type="button"
                                onClick={onRetry}
                                className="flex items-center gap-2 bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700 transition-colors"
                            >
                                <RefreshCw className="w-4 h-4" />
                                Thử lại
                            </button>
                        )}
                        
                        <a
                            href="/debug/google-oauth"
                            className="flex items-center gap-2 bg-gray-600 text-white px-3 py-2 rounded text-sm hover:bg-gray-700 transition-colors"
                        >
                            <ExternalLink className="w-4 h-4" />
                            Debug OAuth
                        </a>
                    </div>
                </div>
            </div>
        </div>
    )
}
