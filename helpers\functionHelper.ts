export const convertTokensByCredit = (total_tokens: number) => {
    // credits =  Tokens / 1000 
    // làm tròn thành số nguyên
    return Math.ceil(total_tokens / 1000);
};
export const checkMissingFields = (fields: Record<string, any>): string[] => {
    const missingFields: string[] = Object.keys(fields).filter((field) => !fields[field]);
    return missingFields;
}
export const normalizeText = (text: string) => {
    return text
        .normalize("NFD") // Chuyển thành dạng ký tự cơ bản + dấu
        .replace(/[\u0300-\u036f]/g, "") // Loại bỏ dấu
        .toLowerCase(); // Chuyển về chữ thường
};

/**
 * Chuyển đổi byte sang MB
 * @param bytes - Dung lượng tính bằng byte
 * @param decimals - Số chữ số sau dấu phẩy (mặc định là 2)
 * @returns Dung lượng tính bằng MB (kiểu string, ví dụ: "12.34 MB")
 */
export function convertBytesToMB(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 MB';
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(decimals)} MB`;
}
