'use client'

interface LoadingScreenProps {
    message?: string
}

export const LoadingScreen = ({ message = "Đang tải..." }: LoadingScreenProps) => {
    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
                <div className="animate-spin inline-block w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mb-4"></div>
                <h2 className="text-xl font-semibold text-gray-800 mb-2">Đang xác thực domain</h2>
                <p className="text-gray-600">{message}</p>
                
                <div className="mt-6 max-w-md mx-auto">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center">
                            <div className="animate-pulse w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span className="text-sm text-blue-700">Đ<PERSON> kiểm tra domain...</span>
                        </div>
                        <div className="flex items-center mt-2">
                            <div className="animate-pulse w-2 h-2 bg-blue-500 rounded-full mr-2" style={{animationDelay: '0.5s'}}></div>
                            <span className="text-sm text-blue-700">Đang giải mã dữ liệu...</span>
                        </div>
                        <div className="flex items-center mt-2">
                            <div className="animate-pulse w-2 h-2 bg-blue-500 rounded-full mr-2" style={{animationDelay: '1s'}}></div>
                            <span className="text-sm text-blue-700">Đang tải thông tin website...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
