interface GoogleLoginConfig {
  clientId: string
  clientSecret?: string
  enabled: boolean
  websiteId: string
}

/**
 * <PERSON><PERSON><PERSON> redirect URI cho Google OAuth dựa trên môi trường hiện tại
 */
export const getGoogleRedirectUri = (): string => {
  if (typeof window === 'undefined') {
    // Server-side: sử dụng từ env hoặc default
    const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_URL || 'http://localhost:3000'
    return `${baseUrl}/api/auth/callback/google`
  }

  // Client-side: xây dựng từ window.location
  const protocol = window.location.protocol
  const host = window.location.host
  const baseUrl = `${protocol}//${host}`

  // Sử dụng NextAuth.js standard callback path
  const redirectUri = `${baseUrl}/api/auth/callback/google`

  console.log('🔗 Google OAuth redirect URI:', redirectUri)
  console.log('💡 Make sure this URI is configured in Google Console')

  return redirectUri
}

/**
 * <PERSON><PERSON><PERSON> tất cả các redirect URI có thể cần cấu hình trong Google Console
 */
export const getAllPossibleRedirectUris = (): string[] => {
  const baseUrls = [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://agent.trinhxuanthuy.id.vn'
  ]

  const paths = [
    '/api/auth/callback/google',
    '/auth/google/callback',
    '/auth/callback/google',
    '/callback/google'
  ]

  const uris: string[] = []

  baseUrls.forEach(baseUrl => {
    paths.forEach(path => {
      uris.push(`${baseUrl}${path}`)
    })
  })

  return uris
}

export const handleNextAuthGoogleLogin = async (config: GoogleLoginConfig) => {
  if (!config.enabled) {
    throw new Error('Google login is not enabled')
  }

  if (!config.clientId) {
    throw new Error('Google Client ID is not configured')
  }

  const redirectUri = getGoogleRedirectUri()

  // Tạo Google OAuth URL
  const googleAuthUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth')
  googleAuthUrl.searchParams.set('client_id', config.clientId)
  googleAuthUrl.searchParams.set('response_type', 'code')
  googleAuthUrl.searchParams.set('scope', 'openid email profile')
  googleAuthUrl.searchParams.set('redirect_uri', redirectUri)
  googleAuthUrl.searchParams.set('state', config.websiteId)

  console.log('🚀 Redirecting to Google OAuth:', googleAuthUrl.toString())

  // Redirect đến Google OAuth
  window.location.href = googleAuthUrl.toString()
}
