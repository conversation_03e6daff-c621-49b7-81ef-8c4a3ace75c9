'use client'

import { useWebsiteStore } from '@/stores/websiteStore'
import { useRouter } from 'next/navigation'

const ErrorPage = () => {
    const { error, reset } = useWebsiteStore()
    const router = useRouter()

    const handleRetry = () => {
        reset()
        router.push('/')
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-lg text-center">
                <div className="text-red-500 text-6xl mb-4">❌</div>
                <h1 className="text-2xl font-bold text-red-600 mb-4">Lỗi xác thực domain</h1>
                <p className="text-gray-600 mb-6">
                    {error || 'Có lỗi xảy ra trong quá trình xác thực domain'}
                </p>
                
                <div className="space-y-3">
                    <button
                        type="button"
                        onClick={handleRetry}
                        className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                    >
                        Thử lại
                    </button>
                    
                    <button
                        type="button"
                        onClick={() => window.location.reload()}
                        className="w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors"
                    >
                        Tải lại trang
                    </button>
                </div>
                
                <div className="mt-6 text-sm text-gray-500">
                    <p>Nếu vấn đề vẫn tiếp tục, vui lòng liên hệ hỗ trợ</p>
                </div>
            </div>
        </div>
    )
}

export default ErrorPage
