"use client"

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Coins, TrendingUp, Gift } from 'lucide-react';
import { useUserCredits } from '@/hooks/useUserProfile';

interface CreditDisplayProps {
  className?: string;
  showDetails?: boolean;
}

export function CreditDisplay({ className = '', showDetails = true }: CreditDisplayProps) {
  const { credits, isLoading, error } = useUserCredits();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !credits) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <p className="text-sm text-muted-foreground">Unable to load credits</p>
        </CardContent>
      </Card>
    );
  }

  const usagePercentage = (credits.used / credits.total) * 100;
  const bonusPercentage = credits.bonus > 0 ? (credits.bonusUsed / credits.bonus) * 100 : 0;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <Coins className="h-5 w-5 text-yellow-500" />
          Credits
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Main Credits */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Main Credits</span>
            <Badge variant={credits.remaining > 100 ? "default" : "destructive"}>
              {credits.remaining} remaining
            </Badge>
          </div>
          
          <Progress value={usagePercentage} className="h-2" />
          
          {showDetails && (
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Used: {credits.used}</span>
              <span>Total: {credits.total}</span>
            </div>
          )}
        </div>

        {/* Bonus Credits */}
        {credits.bonus > 0 && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium flex items-center gap-1">
                <Gift className="h-3 w-3 text-green-500" />
                Bonus Credits
              </span>
              <Badge variant="secondary">
                {credits.bonus - credits.bonusUsed} remaining
              </Badge>
            </div>
            
            <Progress value={bonusPercentage} className="h-2" />
            
            {showDetails && (
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>Used: {credits.bonusUsed}</span>
                <span>Total: {credits.bonus}</span>
              </div>
            )}
          </div>
        )}

        {/* Usage Stats */}
        {showDetails && (
          <div className="pt-2 border-t">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3" />
              <span>
                Total Available: {credits.remaining + (credits.bonus - credits.bonusUsed)} credits
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Compact version for header/sidebar
export function CreditBadge({ className = '' }: { className?: string }) {
  const { credits, isLoading } = useUserCredits();

  if (isLoading || !credits) {
    return (
      <Badge variant="outline" className={className}>
        <Coins className="h-3 w-3 mr-1" />
        Loading...
      </Badge>
    );
  }

  const totalRemaining = credits.remaining + (credits.bonus - credits.bonusUsed);

  return (
    <Badge 
      variant={totalRemaining > 100 ? "default" : "destructive"} 
      className={className}
    >
      <Coins className="h-3 w-3 mr-1" />
      {totalRemaining} credits
    </Badge>
  );
}
