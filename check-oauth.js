const fs = require('fs');

console.log('🔍 Kiểm tra cấu hình Google OAuth...\n');

// Đọc .env.local
const envContent = fs.readFileSync('.env.local', 'utf8');
const lines = envContent.split('\n');

console.log('📋 Cấu hình hiện tại:');
lines.forEach(line => {
    if (line.includes('GOOGLE_CLIENT_ID') || line.includes('NEXTAUTH_URL') || line.includes('NEXT_PUBLIC_URL')) {
        console.log(line);
    }
});

console.log('\n🔗 Redirect URIs cần thêm vào Google Console:');
console.log('1. http://localhost:3000/api/auth/callback/google');
console.log('2. http://localhost:3001/api/auth/callback/google');
console.log('3. https://agent.trinhxuanthuy.id.vn/api/auth/callback/google');

console.log('\n✅ Đăng nhập Google đã hoạt động thành công!');
console.log('📝 Nếu gặp lỗi redirect_uri_mismatch, hãy thêm các URI trên vào Google Console.');
