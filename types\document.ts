export type DocumentDataType = {
    _id: string
    shop_id: number
    vector_id: string
    dataset_id: string
    name: string
    size: number
    type: string
    word_count: number
    status: number
    credits: number
    segments: number
    user_name: string
    created_by: number
    updated_by: string | number
    created_at: string
    updated_at: string
}
export type SegmentDataType = {
    id: string
    payload: PayloadSegmentType
}
export type PayloadSegmentType = {
    position: number
    source: string
    content: string
    document_id: string
    segment_id: string
    word_count: number
    credits: number
    status: number
}

export interface ProcessDocumentSSE {
    shop_id?: string;
    user_id?: string;
    dataset_id?: string;
    name?: string;
    description?: string;
    settings?: SettingDocumentType
}
export interface SettingDocumentType {
    parsingStrategy?: any
    contentFiltering?: any,
    segmentationStrategy?: any,
    segmentId?: any,
    maxSegmentLength?: any,
    segmentationOverlap?: any,
    replaceSpaces?: any,
    deleteUrls?: any,
    extractedContent?: any
}