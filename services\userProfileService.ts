import { EP } from '@/configs/constants/api';
import { getApiEndpoint } from '@/helpers/handleApiUrl';
import axiosClient from '@/lib/axios';
import { UserProfileResponse, ApiResponse } from '@/types/api';

/**
 * Service để xử lý User Profile API
 */
export class UserProfileService {
  /**
   * Lấy thông tin profile của user
   * @param token - JWT token
   * @returns Promise<UserProfileResponse>
   */
  static async getUserProfile(token: string): Promise<UserProfileResponse> {
    try {
      console.log('🔍 Fetching user profile...');

      const baseURL = process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL!;
      const client = axiosClient(baseURL);
      const endpoint = getApiEndpoint([EP.API, EP.V1, EP.USER, EP.PROFILE]);

      const response = await client.get<UserProfileResponse>(endpoint, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ User profile fetched successfully:', response.data);
      return response.data;

    } catch (error) {
      console.error('❌ Failed to fetch user profile:', error);
      throw new Error('Failed to fetch user profile');
    }
  }

  /**
   * Kiểm tra token có hợp lệ không
   * @param token - JWT token
   * @returns Promise<boolean>
   */
  static async validateToken(token: string): Promise<boolean> {
    try {
      const profile = await this.getUserProfile(token);
      return profile.error === false && profile.status === 200;
    } catch (error) {
      console.error('❌ Token validation failed:', error);
      return false;
    }
  }

  /**
   * Lấy thông tin credit của user
   * @param token - JWT token
   * @returns Promise<{credit: number, credit_use: number, remaining: number}>
   */
  static async getUserCredits(token: string): Promise<{
    credit: number;
    credit_use: number;
    remaining: number;
    credits_bonus: number;
    credits_bonus_used: number;
  }> {
    try {
      const profile = await this.getUserProfile(token);

      if (profile.error === false && profile.data.package) {
        const pkg = profile.data.package;
        return {
          credit: pkg.credit,
          credit_use: pkg.credit_use,
          remaining: pkg.credit - pkg.credit_use,
          credits_bonus: pkg.credits_bonus,
          credits_bonus_used: pkg.credits_bonus_used
        };
      }

      throw new Error('Invalid profile response');
    } catch (error) {
      console.error('❌ Failed to get user credits:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin shop của user
   * @param token - JWT token
   * @returns Promise<ApiShop>
   */
  static async getUserShop(token: string) {
    try {
      const profile = await this.getUserProfile(token);

      if (profile.error === false && profile.data.shop) {
        return profile.data.shop;
      }

      throw new Error('Shop information not found');
    } catch (error) {
      console.error('❌ Failed to get user shop:', error);
      throw error;
    }
  }
}
