name: Deploy

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy using SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }} #
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          script: |
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"
            nvm install 20
            nvm use 20
            cd /data/fchat.ai_nextjs
            git reset --hard
            git stash -u
            git pull origin main
            npm run build
            pm2 restart fchat.ai_domain
