"use client";

import React from "react";
import Image from "next/image";

interface UserProfileInfoProps {
    user: {
        name: string;
        email: string;
        avatar?: string;
        credit: number;
        creditUse: number;
    }
}

const UserProfileInfo: React.FC<UserProfileInfoProps> = ({ user }) => {
    const { name, email, avatar, credit, creditUse } = user;
    return (
        <div className="p-4 rounded-lg bg-white shadow-md w-full max-w-md mx-auto">
            {avatar && (
                <Image
                    src={avatar}
                    alt="Avatar"
                    width={64}
                    height={64}
                    className="rounded-full mb-4"
                />
            )}
            <div className="mb-2">
                <span className="font-semibold">Tên:</span> {name}
            </div>
            <div className="mb-2">
                <span className="font-semibold">Email:</span> {email}
            </div>
            <div className="mb-2">
                <span className="font-semibold">Sử dụng credit:</span> {creditUse} / {credit}
            </div>
        </div>
    );
};

export default UserProfileInfo;
