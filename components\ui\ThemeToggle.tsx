// ThemeToggle.tsx
"use client"

import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { Sun, Moon } from "lucide-react"
import * as React from "react"

export function ThemeToggle() {
    const { theme, setTheme } = useTheme()
    const isDark = theme === "dark"

    return (
        <Button
            variant="ghost"
            size="icon"
            aria-label="Chuyển đổi chế độ sáng/tối"
            onClick={() => setTheme(isDark ? "light" : "dark")}
            className="rounded-full"
        >
            <Sun className="h-5 w-5 text-yellow-500 dark:hidden" />
            <Moon className="h-5 w-5 text-gray-700 hidden dark:inline" />
            <span className="sr-only">Chuyển đổi chế độ sáng/tối</span>
        </Button>
    )
} 