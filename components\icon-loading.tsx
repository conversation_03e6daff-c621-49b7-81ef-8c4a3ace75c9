"use client"
import { Loader } from 'lucide-react';
import React from 'react';
interface ILoadingProps {
    width?: string
    height?: string
    className?: string
}
const IconLoading: React.FC<ILoadingProps> = ({ width = "20", height = "20", className }) => {
    return (
        <>
            <div className="animate-spin">
                <Loader height={height} width={width} className={className} />
            </div>
        </>
    );
};

export default IconLoading;
