// src/store/domainStore.ts
import { create } from 'zustand';
import type { DecodedDomainData } from '@/types/domain'; // đường dẫn tùy bạn

interface DomainStore {
    decodedDomainData: DecodedDomainData | null;
    setDecodedDomainData: (data: DecodedDomainData) => void;
    clearDecodedDomainData: () => void;
}

export const useDomainStore = create<DomainStore>((set) => ({
    decodedDomainData: null,
    setDecodedDomainData: (data) => set({ decodedDomainData: data }),
    clearDecodedDomainData: () => set({ decodedDomainData: null }),
}));
