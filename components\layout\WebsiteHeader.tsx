'use client'

import React from 'react'
import { useVerifiedWebsiteInfo } from '@/hooks/useDomainVerification'
import Image from 'next/image'

interface WebsiteHeaderProps {
    className?: string;
    showLogo?: boolean;
    showTitle?: boolean;
    showDescription?: boolean;
}

export const WebsiteHeader: React.FC<WebsiteHeaderProps> = ({
    className = '',
    showLogo = true,
    showTitle = true,
    showDescription = true
}) => {
    const { isVerified, websiteInfo } = useVerifiedWebsiteInfo()

    if (!isVerified || !websiteInfo) {
        return (
            <header className={`bg-white shadow-sm ${className}`}>
                <div className="container mx-auto px-4 py-4">
                    <div className="animate-pulse">
                        <div className="flex items-center space-x-4">
                            {showLogo && (
                                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                            )}
                            <div className="flex-1">
                                {showTitle && (
                                    <div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
                                )}
                                {showDescription && (
                                    <div className="h-4 bg-gray-200 rounded w-64"></div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </header>
        )
    }

    return (
        <header className={`bg-white shadow-sm ${className}`}>
            <div className="container mx-auto px-4 py-4">
                <div className="flex items-center space-x-4">
                    {showLogo && websiteInfo.logo && (
                        <div className="flex-shrink-0">
                            <Image
                                src={websiteInfo.logo}
                                alt={websiteInfo.meta_title || 'Website Logo'}
                                width={48}
                                height={48}
                                className="w-12 h-12 object-contain rounded-lg"
                                onError={(e) => {
                                    console.warn('Failed to load logo:', websiteInfo.logo);
                                    e.currentTarget.style.display = 'none';
                                }}
                            />
                        </div>
                    )}
                    
                    <div className="flex-1 min-w-0">
                        {showTitle && websiteInfo.meta_title && (
                            <h1 className="text-xl font-bold text-gray-900 truncate">
                                {websiteInfo.meta_title}
                            </h1>
                        )}
                        
                        {showDescription && websiteInfo.meta_description && (
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                                {websiteInfo.meta_description}
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </header>
    )
}

// Component đơn giản chỉ hiển thị logo
export const WebsiteLogo: React.FC<{ className?: string; size?: number }> = ({ 
    className = '', 
    size = 48 
}) => {
    const { isVerified, websiteInfo } = useVerifiedWebsiteInfo()

    if (!isVerified || !websiteInfo?.logo) {
        return (
            <div 
                className={`bg-gray-200 rounded-lg animate-pulse ${className}`}
                style={{ width: size, height: size }}
            />
        )
    }

    return (
        <Image
            src={websiteInfo.logo}
            alt={websiteInfo.meta_title || 'Website Logo'}
            width={size}
            height={size}
            className={`object-contain rounded-lg ${className}`}
            style={{ width: size, height: size }}
            onError={(e) => {
                console.warn('Failed to load logo:', websiteInfo.logo);
                e.currentTarget.style.display = 'none';
            }}
        />
    )
}

// Component hiển thị title
export const WebsiteTitle: React.FC<{ 
    className?: string; 
    as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'span' 
}> = ({ 
    className = '', 
    as: Component = 'h1' 
}) => {
    const { isVerified, websiteInfo } = useVerifiedWebsiteInfo()

    if (!isVerified || !websiteInfo?.meta_title) {
        return (
            <div className={`h-6 bg-gray-200 rounded animate-pulse w-48 ${className}`} />
        )
    }

    return (
        <Component className={className}>
            {websiteInfo.meta_title}
        </Component>
    )
}

// Component hiển thị description
export const WebsiteDescription: React.FC<{ className?: string }> = ({ className = '' }) => {
    const { isVerified, websiteInfo } = useVerifiedWebsiteInfo()

    if (!isVerified || !websiteInfo?.meta_description) {
        return (
            <div className={`h-4 bg-gray-200 rounded animate-pulse w-64 ${className}`} />
        )
    }

    return (
        <p className={className}>
            {websiteInfo.meta_description}
        </p>
    )
}
