// Conversation interfaces
export interface ConversationListRequest {
    prompt_id: string;
    page: number;
    limit: number;
}

export interface Conversation {
    _id: string;
    name: string;
    prompt_id: string;
}

export interface ConversationListResponse {
    error: boolean;
    status: number;
    msg: string;
    data: {
        conversations: Conversation[];
        isFullPage: boolean;
    };
}
