"use client"

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useUserProfile } from '@/hooks/useUserProfile'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { User, Settings, Loader2 } from 'lucide-react'
import { LogoutButton } from '@/components/auth/LogoutButton'
import { CreditBadge } from '@/components/user/CreditDisplay'


interface UserInfo {
  name?: string
  email?: string
  image?: string
}

export function UserProfile() {
  const { user, isAuthenticated, logout, isLoading: authLoading } = useAuth()
  const { profile, isLoading: profileLoading } = useUserProfile()
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check if user is logged in
  const isDemoUser = typeof window !== 'undefined' && sessionStorage.getItem('demo_logged_in') === 'true'
  const isLoggedIn = isAuthenticated || isDemoUser

  // Use profile data if available, fallback to auth user data
  const displayUser = profile?.user || user
  const displayName = displayUser?.name || userInfo?.name || 'User'
  const displayEmail = displayUser?.email || userInfo?.email || ''

  useEffect(() => {
    setIsLoading(true)

    if (isAuthenticated && user) {
      // Authenticated user
      const userInfo = {
        name: user.name || '',
        email: user.email || '',
        image: user.avatar || ''
      }
      setUserInfo(userInfo)

    } else if (isDemoUser) {
      // Demo user from sessionStorage
      const localUser = sessionStorage.getItem('demo_user_info')
      if (localUser) {
        try {
          setUserInfo(JSON.parse(localUser))
        } catch (error) {
          console.error('Error parsing demo user info:', error)
          sessionStorage.removeItem('demo_user_info')
          setUserInfo(null)
        }
      }
    } else {
      // No user logged in
      setUserInfo(null)
    }

    setIsLoading(false)
  }, [isAuthenticated, user, isDemoUser])

  // Logout logic moved to LogoutButton component

  // Loading state
  if (authLoading || isLoading) {
    return (
      <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
    )
  }

  // Not logged in
  if (!isLoggedIn || !userInfo) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={(displayUser && 'avatar' in displayUser ? displayUser.avatar : '') || userInfo?.image || '/placeholder-user.jpg'}
              alt={displayName}
            />
            <AvatarFallback>
              {displayName.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {displayName}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {displayEmail}
            </p>
            {/* Credit Badge */}
            <div className="mt-2">
              <CreditBadge />
            </div>
            {isAuthenticated && (
              <p className="text-xs text-green-600">Authenticated</p>
            )}
            {isDemoUser && (
              <p className="text-xs text-blue-600">Demo Account</p>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>Hồ sơ</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>Cài đặt</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <LogoutButton
            variant="ghost"
            size="sm"
            className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
            showIcon={true}
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
