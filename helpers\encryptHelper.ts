// <PERSON><PERSON> <PERSON><PERSON> sử dụng:
// import { decodeJwtPayload } from '@/helpers/encryptHelper';
// const payload = decodeJwtPayload(tokenLogin);
// console.log(payload); // { user_id, shop_id, iat, exp, ... }
import { decryptAesCBC } from '@/lib/crypto'

export const decodePayload = (token: string) => {
  try {
    if (!token || typeof token !== 'string') {
      console.error("Invalid token provided for decoding:", token);
      return null;
    }

    console.log("🔓 Attempting to decode AES encrypted token:", token.substring(0, 50) + "...");

    // Thử giải mã AES trước (đ<PERSON>y là phương thức chính)
    try {
      const aesDecrypted = decryptAesCBC(token);
      if (aesDecrypted) {
        console.log("🔓 AES decryption successful, parsing JSON...");
        const parsedData = JSON.parse(aesDecrypted);
        console.log("✅ Successfully parsed AES decrypted JSON payload");
        return parsedData;
      }
    } catch (aesError) {
      console.log("⚠️ AES decryption failed, trying base64:", aesError);
    }

    // Fallback: Thử base64 decode
    try {
      console.log("🔄 Attempting base64 decode...");
      const base64Decoded = atob(token);
      console.log("📝 Base64 decoded string:", base64Decoded.substring(0, 100) + "...");

      const decodedPayload = JSON.parse(base64Decoded);
      console.log("✅ Successfully parsed base64 JSON payload");
      return decodedPayload;
    } catch (base64Error) {
      console.log("⚠️ Base64 decode failed:", base64Error);
    }

    // Fallback: Thử parse trực tiếp JSON
    try {
      console.log("🔄 Attempting direct JSON parse...");
      const directParsed = JSON.parse(token);
      console.log("✅ Direct JSON parse successful");
      return directParsed;
    } catch (directError) {
      console.log("⚠️ Direct JSON parse failed:", directError);
    }

    console.error("❌ All decoding methods failed for token");
    return null;

  } catch (error) {
    console.error("❌ Error in decodePayload:", error);
    console.error("🔍 Token that failed to decode:", token);
    return null;
  }
}

export const decodeJwtPayload = (token: string) => {
  try {
    const payload = token.split('.')[1];
    const pad = payload.length % 4;
    const base64 = pad ? payload + '='.repeat(4 - pad) : payload;
    return JSON.parse(atob(base64));
  } catch (err) {
    console.error('JWT decode error:', err);
    return null;
  }
}