
1. GET xác thực Domain: https://fchatai-api.salekit.com:3034/api/v1/global/prompt/domain?domain=agent.lienvu.com
 - reponse:
{
    "error": false,
    "status": 200,
    "msg": "<PERSON><PERSON>y thông tin thành công!",
    "data": "chuỗi mã hóa domain"
}

data domain sau khi giải mã
{
    "prompt": {
        "setting": {
            "suggest": [],
            "main_color": "#00a6b1",
            "text_color": "#ffffff",
            "first_mess_time": "1",
            "first_mess_retime": "1",
            "option_chat_corner": "1",
            "icon_width": 40,
            "margin_x": "16",
            "margin_y": "16",
            "margin_x_mobile": "16",
            "margin_y_mobile": "16",
            "icon": "https://photo.salekit.com/uploads/salekit_393b5dd8b53bcc4f7439610ecc840709/favicon1.png?v=1",
            "icon_height": 40,
            "display_greeting": "1",
            "allows_drag": "1",
            "btn_start": "Bắt đầu chat123",
            "greeting": "<PERSON><PERSON><PERSON> mứng bạn đến với fchai.ai! Vui lòng cung cấp thông tin của bạn và bắt đầu trò chuyện.123",
            "place_input": "Nhập nội dung để chat123",
            "slogan": "Chat với chúng tôi123"
        },
        "website": {
            "status": 3,
            "domain": "agent.trinhxuanthuy.id.vn",
            "meta_title": "AI Lien Vu123",
            "meta_description": "AI Lien Vu - Trợ lý ảo thông minh123",
            "favicon": "https://photo.salekit.com/uploads/salekit_393b5dd8b53bcc4f7439610ecc840709/favicon1.png?v=1",
            "logo": "https://fchat.ai/assets/images/logo-emoi.png",
            "thumbnail": "https://fchat.ai/assets/images/logo-emoi.png"
        },
        "is_public": 1,
        "type": 1,
        "credits": 0,
        "credits_bonus": 0,
        "users": null,
        "conversations": 178,
        "favourites": 1,
        "shares": null,
        "created_at": "2025-06-03T10:01:45.000Z",
        "updated_at": "2025-06-03T17:18:41.000Z",
        "_id": "6842957e3545562647c422f0",
        "id": "1368",
        "id_hash": "266f2b3d-d760-494e-a5b3-62c6e4bc61cf",
        "name": "Trợ lý LLM by Thuytx",
        "package_id": "0",
        "likes": "0",
        "uses": "0",
        "lang_code": "",
        "description": "Trợ lý LLM by Thuytx",
        "icon": "",
        "help_url": "",
        "result": null,
        "status": "0",
        "options": null,
        "option_status": "0",
        "avatar_url": "https://fchat.ai/assets/images/logo-emoi.png",
        "bookmark": "",
        "question": null,
        "thread_id": "",
        "one_step": "0",
        "model_type": "gpt-4o-mini",
        "chat_initiation_tool": "",
        "instruction": "",
        "file": "",
        "assistant_id": "",
        "auto_suggestion": "Considering the AI's character settings, the user's previous chat history with the AI assistant, think about the user's scenario, intention, background in their last inquiry, and generate the questions that the user is most likely to ask the AI assistant (you) next.\r\n1. Do not generate questions that the user may already know the answer, or unrelated to the current topics.\r\n2. Always generate very brief and clear questions (less than 15 words) that the user may ask the AI assistant (you), NOT questions that the AI assistant (you) asks the user.\r\n3. DO NOT generate the same or similar questions.\r\n\r\nAdditional requirements:\r\n1. Generate 3 questions each time \r\n2. If the latest user's question involves creative tasks (like coming up with a title), give at least one of the questions that directly asks about enhancing the creativity or attractiveness of the AI assistant's previous answer.\r\n3. If the AI assistant did not or refused to answer the user's question, generate suggestions based on what the assistant can answer to guide the topic in a more productive direction, unrelated to the current topic.\r\n4. Ensure the questions are different from the chat history.",
        "shortcut_button": null,
        "opening_text": "",
        "vector_store_ids": "",
        "shop_id": "684296d83545562647c5a712",
        "shop_id_owner": "684296d83545562647c5a712"
    },
    "website": {
        "type": "personal",
        "google_login": 2,
        "client_id": "************-0k4tudbtnnnv1ce8dkt19dov8n3ffmld.apps.googleusercontent.com",
        "client_secret": "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"
        website_id: "684296d93545562647c5f213";
    }
}


2. POST Login google: https://fchatai-api.salekit.com:3034/api/v1/user/login/google
 - request: {
    "email":"<EMAIL>",
    "name":"Tuấn Vũ Nguyễn",
    "avatar":"",
    "website_id":"684296d93545562647c5f213",
    "ref":"thuytx",
    "google_id":""
} 


- reponse: {
    "error": false,
    "status": 200,
    "msg": "Đăng nhập thành công!",
    "data": "chuỗi mã hóa login google"
}


3. POST Login Gmail/Password: https://fchatai-api.salekit.com:3034/api/v1/user/login
 - request: {
    "email":"<EMAIL>",
    "password":"123456"
}

- reponse: {
    "error": false,
    "status": 200,
    "msg": "Đăng nhập thành công!",
    "data": "chuỗi mã hóa login"
}


4. GET user: https://fchatai-api.salekit.com:3034/api/v1/user/profile
- gửi lên token
- reponse: {
    "error": false,
    "status": 200,
    "msg": "Thành công",
    "data": {
        "user": {
            "_id": "687e1647ec1fc4f8e90cceb2",
            "name": "Tuấn Vũ Nguyễn",
            "email": "<EMAIL>",
            "username": "tuanvunguyen684296d93545562647c5f213t1l3x",
            "email_verify": 1,
            "google_id": "100728407391450329812",
            "website_id": "684296d93545562647c5f213",
            "ref": null,
            "status": 1
        },
        "shop": {
            "status": 1,
            "created_at": "2025-07-24T13:50:33.957Z",
            "updated_at": "2025-07-21T10:28:23.409Z",
            "_id": "687e1647ec1fc4f8e90cceb3",
            "website_id": "684296d93545562647c5f213",
            "name": "Tuấn Vũ Nguyễn",
            "email": "<EMAIL>",
            "username": "tuanvunguyen684296d93545562647c5f213fAXon",
            "ref": null,
            "api_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzaG9wX2lkIjoiNjg3ZTE2NDdlYzFmYzRmOGU5MGNjZWIzIiwiaWF0IjoxNzUzMDkzNzAzfQ.0oyFTgCWj-956IBbpj-Ht0zmlW_-Lsks-Bse5zWg7aM"
        },
        "package": {
            "package_id": 0,
            "month": 0,
            "agent": 1,
            "agent_used": 1,
            "capacity": 0,
            "credit": 1000,
            "credit_use": 237,
            "credits_bonus": 0,
            "credits_bonus_used": 0,
            "package_system_id": 0,
            "expired_at": null,
            "created_at": "2025-07-21T10:28:23.413Z",
            "updated_at": "2025-07-21T10:28:23.413Z",
            "_id": "687e1647ec1fc4f8e90cceb5",
            "shop_id": "687e1647ec1fc4f8e90cceb3"
        }
    }
}


5. Get Convesations: https://fchatai-api.salekit.com:3034/api/v1/conversation/prompt/list?prompt_id=6842957e3545562647c422f0&page=1&limit=10
 - gửi lên token
 - reponse: {
    "error": false,
    "status": 200,
    "msg": "Thành công",
    "data": {
        "conversations": [
            {
                "_id": "687e16776b328b29f0cbc762",
                "name": "test 1",
                "prompt_id": "6842957e3545562647c422f0"
            },
            {
                "_id": "687e5e6b022870ca73434cba",
                "name": "bfgfsgnfzfk",
                "prompt_id": "6842957e3545562647c422f0"
            },
            {
                "_id": "687f423733de1b465993e6ae",
                "name": "okkee",
                "prompt_id": "6842957e3545562647c422f0"
            },
            {
                "_id": "687f43e033de1b465993e6ca",
                "name": "hay",
                "prompt_id": "6842957e3545562647c422f0"
            },
            {
                "_id": "687f457b87294967536e3870",
                "name": "test 2",
                "prompt_id": "6842957e3545562647c422f0"
            },
            {
                "_id": "687f45a687294967536e388d",
                "name": "em ăn cơm chưa",
                "prompt_id": "6842957e3545562647c422f0"
            },
            {
                "_id": "687f469a87294967536e38af",
                "name": "com ngon ko",
                "prompt_id": "6842957e3545562647c422f0"
            },
            {
                "_id": "68808e04f5b7beb3f563cde2",
                "name": "test 3: 3+3",
                "prompt_id": "6842957e3545562647c422f0"
            },
            {
                "_id": "6880a4baaa7065d8523cd003",
                "name": "gg",
                "prompt_id": "6842957e3545562647c422f0"
            }
        ],
        "isFullPage": true
    }
}


6. POST Update tên conversation: https://fchatai-api.salekit.com:3034/api/v1/conversation/update
 - request: {
    "id":"687e16776b328b29f0cbc762",
    "name":"test 1"
} gửi cùng token

- reponse: {
    "error": false,
    "status": 200,
    "msg": "Cập nhật thành công!",
    "id": "687e16776b328b29f0cbc762"
}


7. GET delete conversations: https://fchatai-api.salekit.com:3034/api/v1/conversation/deleteConv/687f5973c1ac609f239c6c68
 - gửi lên cùng token
 - reponse: {
    "error": false,
    "status": 200,
    "msg": "Xóa thành công!",
    "id": "687f5973c1ac609f239c6c68"
}


8. GET message: https://fchatai-api.salekit.com:3034/api/v1/message/conversation?conversation_id=687f6706eaf274b2d0104ba1
 - gửi lên cùng token
 - reponse: 
 {
    "error": false,
    "status": 200,
    "msg": "Thành công",
    "data": [
        {
            "_id": "688193be9522ffbb47359458",
            "type": 0,
            "content": "2 + 2 equals 4. If you have more questions or need help with anything else, just let me know!",
            "albums": [],
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "conversation_id": "687f6706eaf274b2d0104ba1",
            "created_at": "2025-07-24T02:00:30.800Z",
            "updated_at": "2025-07-24T02:00:30.800Z",
            "__v": 0
        },
        {
            "_id": "688193bc9522ffbb47359454",
            "type": 1,
            "content": "2+2",
            "albums": [],
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "conversation_id": "687f6706eaf274b2d0104ba1",
            "created_at": "2025-07-24T02:00:28.756Z",
            "updated_at": "2025-07-24T02:00:28.756Z",
            "__v": 0
        },
        {
            "_id": "688192609522ffbb47359417",
            "type": 0,
            "content": "1 + 1 equals 2. If you have any more questions or need further assistance, feel free to ask!",
            "albums": [],
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "conversation_id": "687f6706eaf274b2d0104ba1",
            "created_at": "2025-07-24T01:54:40.993Z",
            "updated_at": "2025-07-24T01:54:40.993Z",
            "__v": 0
        },
        {
            "_id": "6881925f9522ffbb47359413",
            "type": 1,
            "content": "\\1+1",
            "albums": [],
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "conversation_id": "687f6706eaf274b2d0104ba1",
            "created_at": "2025-07-24T01:54:39.187Z",
            "updated_at": "2025-07-24T01:54:39.187Z",
            "__v": 0
        },
        {
            "_id": "6881925c9522ffbb47359406",
            "type": 0,
            "content": "Hello! How can I assist you today?",
            "albums": [],
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "conversation_id": "687f6706eaf274b2d0104ba1",
            "created_at": "2025-07-24T01:54:36.994Z",
            "updated_at": "2025-07-24T01:54:36.994Z",
            "__v": 0
        },
        {
            "_id": "6881925b9522ffbb47359402",
            "type": 1,
            "content": "hello",
            "albums": [],
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "conversation_id": "687f6706eaf274b2d0104ba1",
            "created_at": "2025-07-24T01:54:35.135Z",
            "updated_at": "2025-07-24T01:54:35.135Z",
            "__v": 0
        }
    ]
}


9. POST chat: https://fchatai-api.salekit.com:3034/api/v1/assistant/chat
 - request: {
    "conversation_id": "",
    "query": "5+5",
    "prompt_id": "6842957e3545562647c422f0" ,
    "shop_id": "6879db5c1f5c650f260335b3",
    "version": "gpt-4o-mini",
    "user_id": "6879db5c1f5c650f260335b2"
} gửi cùng token

- reponse: 
{"create_by":"completions","event":"end","content":"","message":{"conversation_id":"688242446dd1e0d4b0e17c18","message_id":"688242496dd1e0d4b0e17c1e"}}
21:25:09.032
{"create_by":"completions","event":"message","content":".","message":false}
21:25:09.018
{"create_by":"completions","event":"message","content":"10","message":false}

