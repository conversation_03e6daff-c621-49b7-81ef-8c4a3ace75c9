@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 50%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 210 100% 50%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Smooth theme transitions */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Disable transitions for specific elements that shouldn't animate */
.no-transition {
  transition: none !important;
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225) transparent;
}

.dark .custom-scrollbar {
  scrollbar-color: rgb(75 85 99) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgb(203 213 225);
  border-radius: 3px;
  border: none;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgb(148 163 184);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Hide scrollbar for webkit browsers when not needed */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Custom scrollbar for general use */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(248 250 252);
}

.dark ::-webkit-scrollbar-track {
  background: rgb(17 24 39);
}

::-webkit-scrollbar-thumb {
  background-color: rgb(203 213 225);
  border-radius: 4px;
  border: 1px solid rgb(248 250 252);
}

.dark ::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99);
  border: 1px solid rgb(17 24 39);
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(148 163 184);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

table {
  border-spacing: 0 !important;
  border-collapse: collapse;
  border-color: inherit !important;
  display: block !important;
  width: max-content !important;
  max-width: 100% !important;
  overflow: auto !important;
  margin-top: 1em;
  margin-bottom: 1em;
  font-size: 0.95rem;
}

thead {
  background-color: #f9fafb;
  text-align: left;
}

.prose th,
.prose td {
  border: 1px solid #e5e7eb; /* màu xám nhạt */
  padding: 0.75rem;
  vertical-align: top;
}

th {
  font-weight: 600;
  background-color: #f3f4f6;
}

/* Dark mode styles for table */
.dark table {
  border-color: #374151; /* màu xám đậm */
}

.dark thead {
  background-color: #1f2937; /* màu xám tối */
}

.dark .prose th,
.dark .prose td {
  border: 1px solid #4b5563; /* màu xám trung bình */
}

.dark th {
  background-color: #111827; /* màu xám đậm hơn */
  color: #d1d5db; /* màu chữ sáng */
}


.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-decoration: none !important;
}


