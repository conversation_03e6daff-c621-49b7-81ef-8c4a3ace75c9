export interface PayloadCreateTag {
    id?: string;
    name: string;
    created_by: string;
    shop_id: string;
    status?: number;
    user_id?: string;
    token?:string
}
export interface ITag {
    _id: string
    shop_id: string
    shop_id_owner: string
    created_by: string
    name: string
    icon: string
    status: number
    created_at: string
    updated_at: string
    type: number
}