"use client"

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/useAuth'
import { Loader2 } from 'lucide-react'

export default function GoogleCallbackPage() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const { loginGoogle } = useAuth()
    const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing')

    useEffect(() => {
        const handleGoogleCallback = async () => {
            try {
                const code = searchParams.get('code')
                const state = searchParams.get('state') // website_id
                const error = searchParams.get('error')

                if (error) {
                    throw new Error(`Google OAuth error: ${error}`)
                }

                if (!code) {
                    throw new Error('Không nhận được authorization code từ Google')
                }

                if (!state) {
                    throw new Error('Không nhận được website_id từ state parameter')
                }

                console.log('🔍 Processing Google OAuth callback:', { code: !!code, state })

                // Import helper function
                const { getGoogleRedirectUri } = await import('@/lib/googleAuth')
                const redirectUri = getGoogleRedirectUri()
                console.log('🔗 Using redirect URI for token exchange:', redirectUri)

                // Gọi Google API để lấy thông tin user
                const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
                        client_secret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET || '',
                        code,
                        grant_type: 'authorization_code',
                        redirect_uri: redirectUri,
                    }),
                })

                if (!tokenResponse.ok) {
                    throw new Error('Không thể lấy access token từ Google')
                }

                const tokenData = await tokenResponse.json()
                const accessToken = tokenData.access_token

                // Lấy thông tin user từ Google
                const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                    },
                })

                if (!userResponse.ok) {
                    throw new Error('Không thể lấy thông tin user từ Google')
                }

                const userData = await userResponse.json()
                console.log('✅ Google user data:', userData)

                // Đăng nhập với API backend
                await loginGoogle({
                    email: userData.email,
                    name: userData.name,
                    avatar: userData.picture || '',
                    google_id: userData.id,
                    ref: 'thuytx'
                })

                setStatus('success')
                toast.success('Đăng nhập Google thành công!')

                // Redirect về trang chủ sau 1 giây
                setTimeout(() => {
                    router.push('/')
                }, 1000)

            } catch (error: any) {
                console.error('❌ Google callback error:', error)
                setStatus('error')
                toast.error(error.message || 'Có lỗi xảy ra khi đăng nhập Google')

                // Redirect về trang login sau 3 giây
                setTimeout(() => {
                    router.push('/login')
                }, 3000)
            }
        }

        handleGoogleCallback()
    }, [searchParams, loginGoogle, router])

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="text-center space-y-6 max-w-md mx-auto">
                <div className="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-xl">
                    {status === 'processing' && (
                        <Loader2 className="w-10 h-10 text-blue-600 animate-spin" />
                    )}
                    {status === 'success' && (
                        <div className="w-10 h-10 text-green-600">✅</div>
                    )}
                    {status === 'error' && (
                        <div className="w-10 h-10 text-red-600">❌</div>
                    )}
                </div>

                <div className="space-y-2">
                    {status === 'processing' && (
                        <>
                            <h2 className="text-2xl font-bold text-gray-900">
                                Đang xử lý đăng nhập Google
                            </h2>
                            <p className="text-gray-600">
                                Vui lòng đợi trong giây lát...
                            </p>
                        </>
                    )}
                    {status === 'success' && (
                        <>
                            <h2 className="text-2xl font-bold text-green-900">
                                Đăng nhập thành công!
                            </h2>
                            <p className="text-green-600">
                                Đang chuyển hướng về trang chủ...
                            </p>
                        </>
                    )}
                    {status === 'error' && (
                        <>
                            <h2 className="text-2xl font-bold text-red-900">
                                Đăng nhập thất bại
                            </h2>
                            <p className="text-red-600">
                                Đang chuyển hướng về trang đăng nhập...
                            </p>
                        </>
                    )}
                </div>

                {status === 'processing' && (
                    <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                        <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                        <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse [animation-delay:0.2s]"></div>
                        <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse [animation-delay:0.4s]"></div>
                    </div>
                )}
            </div>
        </div>
    )
}
