"use client"

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import Header from '@/components/header/Header';
import UserProfileInfo from '@/components/profile/UserProfileInfo';

export default function Home() {
    const [sidebarOpen, setSidebarOpen] = useState(true);
    const { user, isAuthenticated, isLoading } = useAuth();
    const router = useRouter();
    const [currentTopicId, setCurrentTopicId] = useState<string | null>(null);
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        if (isLoading) return;

        // Check for legacy token
        const hasLegacyToken = typeof window !== 'undefined' && localStorage.getItem('fchatai_token');

        // Only redirect if user is not authenticated and not on auth-related pages
        if (!isAuthenticated && !hasLegacyToken) {
            const currentPath = window.location.pathname;
            const authPages = ['/login', '/welcome', '/auth'];
            const isOnAuthPage = authPages.some(page => currentPath.startsWith(page));

            if (!isOnAuthPage) {
                router.replace('/welcome');
            }
        }
    }, [isLoading, isAuthenticated, router]);

    useEffect(() => {
        function handleResize() {
            setIsMobile(window.innerWidth < 1024);
            if (window.innerWidth < 1024) setSidebarOpen(false);
        }
        window.addEventListener('resize', handleResize);
        if (typeof window !== 'undefined') {
            setIsMobile(window.innerWidth < 1024);
            if (window.innerWidth < 1024) setSidebarOpen(false);
        }
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const hasLegacyToken = typeof window !== 'undefined' && localStorage.getItem('fchatai_token');
    if (isLoading || (!isAuthenticated && !hasLegacyToken)) return null;

    return (
        <div className="flex h-screen w-full overflow-hidden bg-white dark:bg-gray-900">
            <Header />
            {/* <UserProfileInfo /> */}
        </div>
    );
}
