import { useState, useEffect } from 'react';
import { UserProfileService } from '@/services/userProfileService';
import { UserProfileData } from '@/types/api';
import { useAuthStore } from '@/stores/authStore';

interface UseUserProfileReturn {
  profile: UserProfileData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  
  // Helper computed values
  credits: {
    total: number;
    used: number;
    remaining: number;
    bonus: number;
    bonusUsed: number;
  } | null;
  
  isEmailVerified: boolean;
  hasGoogleAccount: boolean;
}

/**
 * Hook để quản lý User Profile
 */
export const useUserProfile = () => {
  const [profile, setProfile] = useState<UserProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { token, isAuthenticated } = useAuthStore();

  const fetchProfile = async () => {
    if (!token || !isAuthenticated) {
      setProfile(null);
      setError('No authentication token');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const response = await UserProfileService.getUserProfile(token);
      
      if (response.error === false && response.data) {
        setProfile(response.data);
        console.log('✅ User profile loaded:', response.data.user.email);
      } else {
        throw new Error(response.msg || 'Failed to load profile');
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('❌ Error loading user profile:', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto fetch khi có token
  useEffect(() => {
    if (token && isAuthenticated) {
      fetchProfile();
    } else {
      setProfile(null);
      setError(null);
    }
  }, [token, isAuthenticated]);

  // Computed values
  const credits = profile?.package ? {
    total: profile.package.credit,
    used: profile.package.credit_use,
    remaining: profile.package.credit - profile.package.credit_use,
    bonus: profile.package.credits_bonus,
    bonusUsed: profile.package.credits_bonus_used
  } : null;

  const isEmailVerified = profile?.user.email_verify === 1;
  const hasGoogleAccount = !!profile?.user.google_id;

  return {
    profile,
    isLoading,
    error,
    refetch: fetchProfile,
    
    // Helper values
    credits,
    isEmailVerified,
    hasGoogleAccount
  };
};

/**
 * Hook đơn giản chỉ để lấy credits
 */
export const useUserCredits = () => {
  const { profile, isLoading, error } = useUserProfile();
  
  return {
    credits: profile?.package ? {
      total: profile.package.credit,
      used: profile.package.credit_use,
      remaining: profile.package.credit - profile.package.credit_use,
      bonus: profile.package.credits_bonus,
      bonusUsed: profile.package.credits_bonus_used
    } : null,
    isLoading,
    error
  };
};

/**
 * Hook để lấy thông tin shop
 */
export const useUserShop = () => {
  const { profile, isLoading, error } = useUserProfile();
  
  return {
    shop: profile?.shop || null,
    apiToken: profile?.shop?.api_token || null,
    isLoading,
    error
  };
};
