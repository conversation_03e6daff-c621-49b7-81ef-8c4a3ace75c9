export type DatasetType = {
  _id: string
  shop_id: number
  type: string
  name: string
  description: string
  prompts: number
  documents: number
  embedding_model: string
  embedding_model_provider: string
  created_by: string
  updated_by: string
  created_at: string
  updated_at: string
  isSelected: boolean
  tag_ids?: Array<string> 
}
export type PageDatasetType = {
  name: string;
  type: string;
  description: string
  dataset_id: string;
  page_id: string;
  vector_id: string;
  status: number;
}
export type TabType = "document" | "settings" | "segments" | 'add-file'

export interface FileData {
  name: string;
  size: number;
  type: string;
  file?: File;
}

export interface CreationSettings {
  parsingStrategy: 'precision' | 'quick';
  // Chỉ có khi parsingStrategy === 'precision'
  extractedContent?: {
    imageElements: boolean;
    tableElements: boolean;
  };
  contentFiltering?: string;

  segmentationStrategy: 'automatic' | 'custom' | 'hierarchy';
  // Chỉ có khi segmentationStrategy === 'custom'
  segmentId?: string;
  maxSegmentLength?: number;
  segmentationOverlap?: number;
  replaceSpaces?: boolean;
  deleteUrls?: boolean;
}

export interface Step {
  number: number;
  title: string;
  completed: boolean;
}

export interface IDataMessage {
  event: string,
  error: boolean
  msg: string
  status: number
  data: {
    dataset_id?: string
    document_id?: string
    segments: string[]
    segment: string
    file: {
      file: string
      name?: string
      fieldname: string
      originalname?: string
      encoding: string
      mimetype: string
      destination: string
      filename: string
      path: string
      size: number
    }
  }
}