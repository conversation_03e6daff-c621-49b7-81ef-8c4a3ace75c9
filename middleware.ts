import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  console.log(`🔍 Middleware: ${pathname}`)

  // Tất cả các route khác đều được phép truy cập (domain verification sẽ được xử lý bởi DomainAuthProvider)
  return NextResponse.next()
}

export const config = {
  matcher: ['/((?!_next|favicon.ico|robots.txt|sitemap.xml).*)'], // áp dụng cho mọi route trừ static files
}
