#!/usr/bin/env node

/**
 * Script để kiểm tra cấu hình Google OAuth và hiển thị thông tin debug
 */

const fs = require('fs');
const path = require('path');

// Đọc file .env.local
function readEnvFile() {
    const envPath = path.join(process.cwd(), '.env.local');
    if (!fs.existsSync(envPath)) {
        console.error('❌ File .env.local không tồn tại');
        return {};
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
            const [key, ...valueParts] = trimmed.split('=');
            if (key && valueParts.length > 0) {
                envVars[key] = valueParts.join('=');
            }
        }
    });

    return envVars;
}

// <PERSON>ểm tra cấu hình
function checkGoogleOAuthConfig() {
    console.log('🔍 Kiểm tra cấu hình Google OAuth...\n');

    const env = readEnvFile();
    
    // Kiểm tra các biến môi trường cần thiết
    const requiredVars = [
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'NEXT_PUBLIC_GOOGLE_CLIENT_ID',
        'NEXTAUTH_URL',
        'NEXT_PUBLIC_URL'
    ];

    console.log('📋 Biến môi trường:');
    requiredVars.forEach(varName => {
        const value = env[varName];
        if (value) {
            console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
        } else {
            console.log(`❌ ${varName}: Chưa được cấu hình`);
        }
    });

    console.log('\n🔗 Redirect URIs cần cấu hình trong Google Console:');
    
    const baseUrls = [
        env.NEXTAUTH_URL || 'http://localhost:3000',
        env.NEXT_PUBLIC_URL || 'http://localhost:3001',
        'http://localhost:3000',
        'http://localhost:3001',
        'https://agent.trinhxuanthuy.id.vn'
    ];

    // Loại bỏ duplicate
    const uniqueUrls = [...new Set(baseUrls)];
    
    uniqueUrls.forEach((baseUrl, index) => {
        const redirectUri = `${baseUrl}/api/auth/callback/google`;
        console.log(`${index + 1}. ${redirectUri}`);
    });

    console.log('\n📝 Hướng dẫn cấu hình:');
    console.log('1. Truy cập: https://console.cloud.google.com/apis/credentials');
    console.log('2. Chọn OAuth 2.0 Client ID của bạn');
    console.log('3. Thêm tất cả các URI ở trên vào "Authorized redirect URIs"');
    console.log('4. Lưu cấu hình và thử lại');

    console.log('\n🔧 Debug URLs:');
    console.log(`- Debug page: ${env.NEXT_PUBLIC_URL || 'http://localhost:3001'}/debug/google-oauth`);
    console.log(`- Login page: ${env.NEXT_PUBLIC_URL || 'http://localhost:3001'}/login`);

    // Kiểm tra Client ID
    if (env.NEXT_PUBLIC_GOOGLE_CLIENT_ID) {
        console.log('\n🆔 Client ID Info:');
        console.log(`Client ID: ${env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}`);
        
        // Kiểm tra format Client ID
        if (env.NEXT_PUBLIC_GOOGLE_CLIENT_ID.includes('.apps.googleusercontent.com')) {
            console.log('✅ Client ID có format hợp lệ');
        } else {
            console.log('⚠️ Client ID có thể không đúng format');
        }
    }

    console.log('\n💡 Lưu ý:');
    console.log('- Sau khi cập nhật Google Console, đợi vài phút để thay đổi có hiệu lực');
    console.log('- Kiểm tra console log trong browser để xem thông tin chi tiết');
    console.log('- Sử dụng trang /debug/google-oauth để kiểm tra real-time');
}

// Chạy script
if (require.main === module) {
    checkGoogleOAuthConfig();
}

module.exports = { checkGoogleOAuthConfig, readEnvFile };
