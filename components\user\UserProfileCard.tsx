"use client"

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Mail, User, Shield, Calendar } from 'lucide-react';
import { UserProfileData } from '@/types/api';

interface UserProfileCardProps {
  profile: UserProfileData;
  className?: string;
}

export function UserProfileCard({ profile, className = '' }: UserProfileCardProps) {
  const { user, shop, package: pkg } = profile;

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Avatar className="h-12 w-12">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="text-lg font-semibold">{user.name}</h3>
            <p className="text-sm text-muted-foreground">@{user.username}</p>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* User Info */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{user.email}</span>
            {user.email_verify === 1 && (
              <Badge variant="secondary" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            )}
          </div>
          
          {user.google_id && (
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Linked with Google</span>
              <Badge variant="outline" className="text-xs">Google</Badge>
            </div>
          )}
        </div>

        {/* Shop Info */}
        <div className="border-t pt-4">
          <h4 className="font-medium mb-2">Shop Information</h4>
          <div className="space-y-1 text-sm text-muted-foreground">
            <p>Shop ID: {shop._id}</p>
            <div className="flex items-center gap-2">
              <Calendar className="h-3 w-3" />
              <span>Created: {formatDate(shop.created_at)}</span>
            </div>
          </div>
        </div>

        {/* Package Info */}
        <div className="border-t pt-4">
          <h4 className="font-medium mb-2">Package Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Credits</p>
              <p className="font-medium">{pkg.credit - pkg.credit_use} / {pkg.credit}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Agents</p>
              <p className="font-medium">{pkg.agent_used} / {pkg.agent}</p>
            </div>
            {pkg.credits_bonus > 0 && (
              <div className="col-span-2">
                <p className="text-muted-foreground">Bonus Credits</p>
                <p className="font-medium">{pkg.credits_bonus - pkg.credits_bonus_used} / {pkg.credits_bonus}</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
