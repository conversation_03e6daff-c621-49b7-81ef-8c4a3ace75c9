"use client"

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center px-4">
      <div className="max-w-md mx-auto text-center bg-white p-8 rounded-lg shadow-lg">
        <div className="text-6xl mb-4">❌</div>
        <h1 className="text-2xl font-bold text-red-600 mb-4">Có lỗi xảy ra</h1>
        <p className="text-gray-600 mb-6">
          Không thể xác thực domain hoặc có lỗi hệ thống.
        </p>
        <div className="space-y-3">
          <button
            type="button"
            onClick={reset}
            className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            <PERSON><PERSON><PERSON> lạ<PERSON>
          </button>
          <a
            href="/verify"
            className="block w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors"
          >
            Xác thực lại domain
          </a>
        </div>
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4 text-left">
            <summary className="cursor-pointer text-sm text-gray-500">Chi tiết lỗi (dev)</summary>
            <pre className="text-xs text-red-500 mt-2 overflow-auto">
              {error.message}
            </pre>
          </details>
        )}
      </div>
    </div>
  )
}
